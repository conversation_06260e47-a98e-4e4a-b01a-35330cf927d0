{"name": "svm-iam", "version": "0.0.1", "description": "", "author": "LongVu", "private": true, "license": "ISC", "engines": {"node": ">=14"}, "scripts": {"prepare": "husky install", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "build:dev": "nest build", "build:staging": "nest build", "build:uat": "nest build", "build:production": "nest build", "build:testing": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "rimraf dist && cross-env NODE_ENV=local nest start --watch", "start:dev": "cross-env NODE_ENV=dev nest start --watch", "start:debug": "nest start --debug --watch", "start:testing": "cross-env NODE_ENV=testing nest start --watch", "start:prod": "node dist/main", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "lint": "eslint . --ext .ts", "typeorm": "ts-node node_modules/typeorm/cli.js  -f ormconfig", "migration:generate": "yarn run typeorm migration:generate -d src/migrations -n", "migration:create": "yarn run typeorm migration:create -d src/migrations -n", "migration:revert": "yarn run typeorm migration:revert", "migration:run": "yarn run typeorm migration:run"}, "husky": {"hooks": {"pre-commit": "yarn lint"}}, "dependencies": {"@nestjs/bull": "^0.4.1", "@nestjs/common": "^7.5.1", "@nestjs/config": "^0.5.0", "@nestjs/core": "^7.5.1", "@nestjs/mongoose": "^7.1.0", "@nestjs/platform-express": "^7.5.1", "@nestjs/swagger": "^4.7.5", "@nestjs/terminus": "^8.0.0", "@nestjs/typeorm": "^7.1.5", "async": "^3.2.0", "atob": "^2.1.2", "aws-sdk": "^2.702.0", "axios": "^0.21.2", "bluebird": "^3.5.1", "btoa": "^1.2.1", "bull": "^3.29.0", "cache-manager": "^3.4.4", "camelcase-keys": "^6.2.2", "chalk": "^4.1.0", "chokidar": "^3.5.2", "class-transformer": "0.3.1", "class-validator": "^0.13.1", "compression": "^1.7.4", "dot-object": "^1.7.0", "dotenv": "^16.0.1", "elastic-apm-node": "^4.7.0", "glob": "^7.1.6", "helmet": "^4.6.0", "jsonwebtoken": "^8.5.1", "moment": "^2.29.1", "mongo-cursor-pagination": "^7.1.0", "mongoose": "5.10.3", "morgan": "^1.10.0", "multer": "^1.4.1", "multer-s3": "^2.9.0", "nestjs-i18n": "^8.1.4", "nestjs-redis": "^1.3.3", "numeral": "^2.0.6", "object-mapper": "^5.0.0", "pg": "^8.7.1", "reflect-metadata": "^0.1.13", "rimraf": "^3.0.2", "rxjs": "^6.6.6", "signale": "^1.4.0", "snakecase-keys": "^3.2.0", "svm-nest-lib": "2.2.3", "swagger-ui-express": "^4.1.5", "typeorm": "^0.2.37", "yamljs": "^0.3.0"}, "devDependencies": {"@nestjs/cli": "^7.5.1", "@nestjs/schematics": "^7.1.3", "@nestjs/testing": "^7.5.1", "@types/async": "^3.2.5", "@types/bluebird": "^3.5.20", "@types/bull": "^3.15.4", "@types/cache-manager": "^3.4.2", "@types/dot-object": "^1.5.0", "@types/express": "^4.17.8", "@types/glob": "^7.1.3", "@types/jest": "^26.0.15", "@types/lodash": "^4.14.105", "@types/moment": "^2.13.0", "@types/mongoose": "5.3.20", "@types/multer": "^1.3.7", "@types/multer-s3": "^2.7.6", "@types/node": "^14.14.6", "@types/numeral": "^0.0.22", "@types/signale": "^1.4.1", "@types/supertest": "^2.0.10", "@types/yamljs": "^0.2.31", "@typescript-eslint/eslint-plugin": "^4.8.1", "@typescript-eslint/parser": "^4.8.1", "cross-env": "^7.0.3", "eslint": "^7.13.0", "eslint-config-prettier": "^6.15.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-prettier": "^3.1.4", "eslint-watch": "^7.0.0", "husky": "^7.0.2", "jest": "^26.6.3", "nodemon": "^1.17.2", "prettier": "^2.1.2", "supertest": "^6.0.0", "ts-jest": "^26.4.3", "ts-loader": "^8.0.8", "ts-node": "^9.0.0", "tsconfig-paths": "^3.9.0", "typescript": "^4.0.5"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}