import { Injectable } from '@nestjs/common';
import { HealthCheckService, TypeOrmHealthIndicator } from '@nestjs/terminus';

@Injectable()
export class HealthService {
  constructor(
    private healthCheck: HealthCheckService,
    private typeOrmHealth: TypeOrmHealthIndicator,
  ) {}

  healthCheckCluster() {
    return this.healthCheck.check([
      () => this.typeOrmHealth.pingCheck('database', { timeout: 10000 }),
    ]);
  }
}
