/**
 * <AUTHOR>
 * @module app
 * @since 2020-11-13
 */

import { Module } from '@nestjs/common';
import { HealthCheckService, TypeOrmHealthIndicator } from '@nestjs/terminus';
import { HealthCheckExecutor } from '@nestjs/terminus/dist/health-check/health-check-executor.service';

import { HealthService } from './health.service';
import { HealthController } from './health.controller';

@Module({
  imports: [],
  controllers: [HealthController],
  providers: [HealthService, HealthCheckService, TypeOrmHealthIndicator, HealthCheckExecutor],
})
export class HealthModule {}
