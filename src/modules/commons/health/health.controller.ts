import { ApiTags } from '@nestjs/swagger';
import { HealthCheck } from '@nestjs/terminus';
import { Get, Controller } from '@nestjs/common';
import { HealthService } from './health.service';

@ApiTags('Health')
@Controller('health')
export class HealthController {
  constructor(private readonly healthService: HealthService) {}

  @Get('kube-startup')
  @HealthCheck()
  healthCheckStartupCluster() {
    return this.healthService.healthCheckCluster();
  }

  @Get('kube-liveness')
  @HealthCheck()
  healthCheckLivenessCluster() {
    return this.healthService.healthCheckCluster();
  }

  @Get('kube-readiness')
  @HealthCheck()
  healthCheckReadinessCluster() {
    return this.healthService.healthCheckCluster();
  }
}
