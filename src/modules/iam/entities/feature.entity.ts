import { IdentifyEntity } from 'svm-nest-lib';
import { Entity, Column, ManyToOne, OneToMany, Index } from 'typeorm';
import { DBIndexes } from '../../../commons/consts/db.const';
import { Permission } from './permission.entity';

@Entity()
@Index(DBIndexes.IDX_FEATURE_NAME, ['name'], {
  unique: true,
  where: 'deleted = false',
})
export class Feature extends IdentifyEntity {
  @Column()
  public name: string;

  @Column({ nullable: true })
  public description?: string;

  @Column({ type: 'uuid', nullable: true })
  public parentId?: string;

  @Column({ default: 0 })
  public order: number; // For custom sorting features

  @Column({ default: true })
  public isLeaf: boolean;

  @ManyToOne(() => Feature, (feature) => feature.children)
  parent?: Feature;

  @OneToMany(() => Feature, (feature) => feature.parent)
  children?: Feature[];

  @OneToMany(() => Permission, (permission) => permission.feature)
  permissions?: Permission[];
}
