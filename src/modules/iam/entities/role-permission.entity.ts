import { IdentifyEntity } from 'svm-nest-lib';
import { Entity, ManyToOne, Column, Index } from 'typeorm';
import { DBIndexes } from '../../../commons/consts/db.const';
import { Permission } from './permission.entity';
import { Role } from './role.entity';

@Entity()
@Index(DBIndexes.IDX_ROLE_PERMISSION_ROLE_PERMISSION, ['roleId', 'permissionId'], {
  unique: true,
  where: 'deleted = false',
})
export class RolePermission extends IdentifyEntity {
  @Column({ type: 'uuid' })
  public roleId: string;

  @Column({ type: 'uuid' })
  public permissionId: string;

  // @Column({ default: true })
  // public isEnable: boolean;

  @ManyToOne(() => Role, (role) => role.rolePermissions, { onDelete: 'CASCADE' })
  role: Role;

  @ManyToOne(() => Permission, (permission) => permission.rolePermissions, { onDelete: 'CASCADE' })
  permission: Permission;
}
