import { IdentifyEntity } from 'svm-nest-lib';
import { Entity, ManyToOne, Column } from 'typeorm';
import { Permission } from './permission.entity';
import { Role } from './role.entity';

@Entity()
export class UserRolePermission extends IdentifyEntity {
  @Column({ nullable: false })
  userId: string;

  @Column({ type: 'uuid' })
  public roleId: string;

  @Column({ type: 'uuid' })
  public permissionId: string;

  @Column()
  featureName: string;

  @Column()
  actionName: string;

  @ManyToOne(() => Role, (role) => role.userRolePermissions, { onDelete: 'CASCADE' })
  role: Role;

  @ManyToOne(() => Permission, (permission) => permission.userRolePermissions, {
    onDelete: 'CASCADE',
  })
  permission: Permission;
}

/**
 * Create index manually:
 * 1. Covering index for searching permissions of given userId
 * CREATE INDEX idx_user-role-permission_userId_i_permission ON user_role_permission("userId")
 *   INCLUDE ("featureName", "actionName");
 */
