import { IdentifyEntity } from 'svm-nest-lib';
import { Enti<PERSON>, ManyToOne, Column, Unique } from 'typeorm';
import { Role } from './role.entity';

@Entity()
@Unique(['userId', 'roleId'])
export class UserRole extends IdentifyEntity {
  @Column({ type: 'uuid' })
  userId: string;

  @Column({ type: 'uuid' })
  public roleId: string;

  @ManyToOne(() => Role, (role) => role.userRoles, { onDelete: 'CASCADE' })
  role: Role;
}

/**
 * Create index manually:
 * 1. Index for searching roles of a given user
 * CREATE INDEX idx_user-role_userId ON user_role("userId");
 */
