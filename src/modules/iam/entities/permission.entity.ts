import { IdentifyEntity } from 'svm-nest-lib';
import { Entity, ManyToOne, OneToMany, Column, Index } from 'typeorm';
import { DBIndexes } from '../../../commons/consts/db.const';
import { Action } from './action.entity';
import { Feature } from './feature.entity';
import { RolePermission } from './role-permission.entity';
import { UserRolePermission } from './user-role-permission.entity';

@Entity()
@Index(DBIndexes.IDX_PERMISSION_FEATURE_ACTION, ['featureId', 'actionId'], {
  unique: true,
  where: 'deleted = false',
})
export class Permission extends IdentifyEntity {
  @Column({ type: 'uuid' })
  public featureId: string;

  @Column({ type: 'uuid' })
  public actionId: string;

  @ManyToOne(() => Feature, (feature) => feature.permissions, { onDelete: 'CASCADE' })
  feature: Feature;

  @ManyToOne(() => Action, (action) => action.permissions, { onDelete: 'CASCADE' })
  action: Action;

  @OneToMany(() => RolePermission, (rolePermission) => rolePermission.permission)
  rolePermissions: RolePermission[];

  @OneToMany(() => UserRolePermission, (userRolePermission) => userRolePermission.permission)
  userRolePermissions: UserRolePermission[];
}
