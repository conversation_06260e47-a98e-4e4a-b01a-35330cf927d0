import { IdentifyEntity } from 'svm-nest-lib';
import { Entity, Column, OneToMany, Index } from 'typeorm';
import { DBIndexes } from '../../../commons/consts/db.const';
import { StatusCommon } from '../../../commons/enums';
import { WorkflowRole } from '../../work-flow/entities/work-flow-role.entity';
import { RolePermission } from './role-permission.entity';
import { UserRolePermission } from './user-role-permission.entity';
import { UserRole } from './user-role.entity';
@Entity()
@Index(DBIndexes.IDX_ROLE_NAME_COMPANY_ID, ['name', 'companyId'], {
  unique: true,
  where: 'deleted = false',
})
@Index(DBIndexes.IDX_ROLE_NAME_COMPANY_ID_NULL, ['name'], {
  unique: true,
  where: '"companyId" is NULL AND deleted = false',
})
export class Role extends IdentifyEntity {
  @Column({ type: 'citext', nullable: false })
  public name: string; // case insensitive unique --> citext

  @Column({ nullable: true })
  public description: string;

  @Column({ nullable: false, default: false })
  public isDefault: boolean;

  @Column({ type: 'enum', enum: StatusCommon, nullable: true, default: StatusCommon.ACTIVE })
  public status: string;

  @Column({ type: 'uuid', nullable: true })
  public companyId: string;

  @Column({ nullable: true })
  public isInspection: boolean;

  @Column({ nullable: true })
  public isQA: boolean;

  @OneToMany(() => RolePermission, (rolePermission) => rolePermission.role)
  rolePermissions: RolePermission[];

  @OneToMany(() => UserRole, (userRole) => userRole.role)
  userRoles: UserRole[];

  @OneToMany(() => UserRolePermission, (userRolePermission) => userRolePermission.role)
  userRolePermissions: UserRolePermission[];

  @OneToMany(() => WorkflowRole, (workflowRole) => workflowRole.role)
  workflowRoles: WorkflowRole[];
}
