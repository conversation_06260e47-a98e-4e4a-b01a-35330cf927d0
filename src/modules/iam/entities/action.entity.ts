import { IdentifyEntity } from 'svm-nest-lib';
import { Entity, Column, OneToMany, Index } from 'typeorm';
import { DBIndexes } from '../../../commons/consts/db.const';
import { Permission } from './permission.entity';

@Entity()
@Index(DBIndexes.IDX_ACTION_NAME, ['name'], { unique: true, where: 'deleted = false' })
export class Action extends IdentifyEntity {
  @Column({ nullable: false })
  public name: string;

  @OneToMany(() => Permission, (permission) => permission.action)
  permissions: Permission[];
}
