import {
  Post,
  Get,
  Body,
  Controller,
  Param,
  Put,
  HttpStatus,
  ParseUUIDPipe,
  UseGuards,
  Query,
} from '@nestjs/common';
import {
  ApiBody,
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { IAMService } from './iam.service';
import { AuthInternalGuard } from 'svm-nest-lib';
import { CreateUserRoleDto } from './dto/create-user-role.dto';
import { CheckTokenDto } from './dto/check-token.dto';
import { ListRolePermissionDto } from './dto';
import { SaveTokenDto } from './dto/save-token.dto';

@ApiTags('Internal role permission')
@Controller('internal/iam')
@ApiBearerAuth()
@UseGuards(AuthInternalGuard)
export class IAMInternalController {
  constructor(private readonly iamService: IAMService) {}

  @ApiParam({ name: 'userId', type: 'string', required: true })
  @ApiResponse({ description: 'Create user role success', status: HttpStatus.CREATED })
  @ApiOperation({ summary: 'Create user role' })
  @ApiBody({ type: CreateUserRoleDto, description: 'Create role object body' })
  @Post('/user/:userId/role')
  async createUserRole(
    @Param('userId', ParseUUIDPipe) userId: string,
    @Body() body: CreateUserRoleDto,
  ) {
    return this.iamService.createUserRole(userId, body);
  }

  @ApiParam({ name: 'userId', type: 'string', required: true })
  @ApiBody({ type: CreateUserRoleDto, description: 'Update role object body' })
  @ApiResponse({
    description: 'Update user role success',
    status: HttpStatus.OK,
  })
  @ApiOperation({ summary: 'Update user role' })
  @Put('/user/:userId/role')
  async updateUserRole(
    @Param('userId', ParseUUIDPipe) userId: string,
    @Body() body: CreateUserRoleDto,
  ) {
    return this.iamService.updateUserRole(userId, body);
  }

  @ApiParam({ name: 'userId', type: 'string', required: true })
  @ApiBody({ type: CreateUserRoleDto, description: 'Check role update is inspector' })
  @ApiResponse({
    description: 'Check role update is inspector',
    status: HttpStatus.OK,
  })
  @ApiOperation({ summary: 'Check role update is inspector' })
  @Post('/user/:userId/check-role-inspector')
  async checkRoleUpdateInspector(
    @Param('userId', ParseUUIDPipe) userId: string,
    @Body() body: CreateUserRoleDto,
  ) {
    return this.iamService.checkRoleUpdateInspector(userId, body);
  }

  @ApiParam({ name: 'userId', type: 'string', required: true })
  @ApiResponse({
    description: 'Get user role success',
    status: HttpStatus.OK,
  })
  @ApiOperation({ summary: 'Get user role' })
  @Get('/user/:userId/role')
  async getAllUserRole(@Param('userId', ParseUUIDPipe) userId: string) {
    return this.iamService.getAllUserRole(userId);
  }

  @ApiParam({ name: 'userId', type: 'string', required: true })
  @ApiResponse({
    description: 'Get user role permission (support workflow permission also) success',
    status: HttpStatus.OK,
  })
  @ApiOperation({ summary: 'Get user role permission (support workflow permission also)' })
  @ApiQuery({
    description: 'Paginate params',
    type: ListRolePermissionDto,
    required: false,
  })
  @Get('/user/:userId/role-permission')
  async getAllUserRolePermission(
    @Param('userId', ParseUUIDPipe) userId: string,
    @Query() query: ListRolePermissionDto,
  ) {
    return this.iamService.getAllUserRolePermission(userId, query);
  }

  @ApiParam({ name: 'userId', type: 'string', required: true })
  @ApiBody({ type: SaveTokenDto, description: 'Save token stateful of given user' })
  @ApiResponse({
    description: 'Save token stateful OK',
    status: HttpStatus.OK,
  })
  @ApiOperation({ summary: 'Check token stateful' })
  @Post('/user/:userId/save-token')
  async saveTokenStateful(
    @Param('userId', ParseUUIDPipe) userId: string,
    @Body() body: SaveTokenDto,
  ) {
    return this.iamService.saveTokenStateful(userId, body);
  }

  @ApiResponse({
    description: 'Delete token stateful OK',
    status: HttpStatus.OK,
  })
  @ApiOperation({ summary: 'Check token stateful' })
  @Post('/user/:userId/delete-token')
  async deleteTokenStateful(
    @Param('userId', ParseUUIDPipe) userId: string,
    @Body() body: SaveTokenDto,
  ) {
    return this.iamService.deleteTokenStateful(userId, body);
  }

  @ApiResponse({
    description: 'Delete token specific OK',
    status: HttpStatus.OK,
  })
  @ApiOperation({ summary: 'Check token stateful' })
  @Post('/user/:userId/delete-token-spec')
  async deleteTokenSpecific(
    @Param('userId', ParseUUIDPipe) userId: string,
    @Body() body: SaveTokenDto,
  ) {
    return this.iamService.deleteTokenSpecific(userId, body);
  }

  @ApiParam({ name: 'userId', type: 'string', required: true })
  @ApiBody({ type: CheckTokenDto, description: 'Check token stateful of given user' })
  @ApiResponse({
    description: 'Check token stateful OK',
    status: HttpStatus.OK,
  })
  @ApiOperation({ summary: 'Check token stateful' })
  @Post('/user/:userId/check-token')
  async checkTokenStateful(
    @Param('userId', ParseUUIDPipe) userId: string,
    @Body() body: CheckTokenDto,
  ) {
    return this.iamService.checkTokenStateful(userId, body);
  }

  @ApiParam({ name: 'userId', type: 'string', required: true })
  @ApiResponse({
    description: 'Get user role with permission (support workflow permission also) success',
    status: HttpStatus.OK,
  })
  @ApiOperation({ summary: 'Get user role with permission (support workflow permission also)' })
  @ApiQuery({
    description: 'Paginate params',
    type: ListRolePermissionDto,
    required: false,
  })
  @Get('/user/:userId/role-with-permissions')
  async getUserRolePermission(
    @Param('userId', ParseUUIDPipe) userId: string,
    @Query() query: ListRolePermissionDto,
  ) {
    return this.iamService.getUserRolePermission(userId, query);
  }
}
