import { Entity<PERSON>ana<PERSON>, EntityRepository, In } from 'typeorm';
import { TypeORMRepository } from 'svm-nest-lib';
import { UserRole } from '../entities/user-role.entity';
import { UserRolePermission } from '../entities/user-role-permission.entity';
import { Permission } from '../entities/permission.entity';
import { StatusCommon } from '../../../commons/enums';
import { ListRolePermissionDto } from '../dto';

@EntityRepository(UserRolePermission)
export class UserRolePermissionRepository extends TypeORMRepository<UserRolePermission> {
  async _createUserRolePermission(inputManager: EntityManager, userId: string, roleIds: string[]) {
    // Find all enable permissions of roleIds (Data format (1))
    const permissions = await (inputManager || this.manager)
      .getRepository(Permission)
      .createQueryBuilder('permission')
      .leftJoinAndSelect('permission.feature', 'feature')
      .leftJoinAndSelect('permission.action', 'action')
      .leftJoinAndSelect('permission.rolePermissions', 'rolePermission')
      .where('rolePermission.roleId IN (:...roleIds)', {
        roleIds,
      })
      .select(['permission.id', 'feature.name', 'action.name', 'rolePermission.roleId'])
      .getMany();

    if (permissions.length > 0) {
      // Save to user_role_permission table
      const userRolePermissions: UserRolePermission[] = [];
      for (let i = 0; i < permissions.length; i++) {
        for (let j = 0; j < permissions[i].rolePermissions.length; j++) {
          userRolePermissions.push({
            userId,
            roleId: permissions[i].rolePermissions[j].roleId,
            permissionId: permissions[i].id,
            featureName: permissions[i].feature.name,
            actionName: permissions[i].action.name,
          } as UserRolePermission);
        }
      }

      await ((inputManager && inputManager.getRepository(UserRolePermission)) || this).save(
        userRolePermissions,
      );
    }
  }

  async _updateWhenUserChangeRole(
    manager: EntityManager,
    userId: string,
    removeRoleIds: string[],
    newRoleIds: string[],
  ) {
    if (removeRoleIds.length > 0) {
      // remove user_role_permission with (userId, roleId)
      await manager.delete(UserRolePermission, { userId, roleId: In(removeRoleIds) });
    }

    if (newRoleIds.length > 0) {
      // add new user_role_permission
      await this._createUserRolePermission(manager, userId, newRoleIds);
    }
  }

  async _updateWhenRoleChangePermission(
    manager: EntityManager,
    roleId: string,
    removePermissionIds: string[],
    newPermissionIds: string[],
  ) {
    // check if no user is affected by this role
    const affectedUsers = await manager
      .getRepository(UserRole)
      .find({ where: { roleId }, select: ['userId'] });

    if (affectedUsers.length === 0) return;

    // remove user_role_permission with (roleId, permissionId)
    if (removePermissionIds.length > 0) {
      await manager.delete(UserRolePermission, { roleId, permissionId: In(removePermissionIds) });
    }

    // Create new user_role_permission entities
    if (newPermissionIds.length > 0) {
      //- Find all data cache of newPermissions
      const permissions = await manager
        .getRepository(Permission)
        .createQueryBuilder('permission')
        .leftJoinAndSelect('permission.feature', 'feature')
        .leftJoinAndSelect('permission.action', 'action')
        .where('permission.id IN (:...newPermissionIds)', { newPermissionIds })
        .select(['permission.id', 'feature.name', 'action.name'])
        .getMany();

      // Save to user_role_permission table
      const preparedUserRolePermissions: UserRolePermission[] = [];
      for (let i = 0; i < permissions.length; i++) {
        for (let j = 0; j < affectedUsers.length; j++) {
          preparedUserRolePermissions.push({
            userId: affectedUsers[j].userId,
            roleId,
            permissionId: permissions[i].id,
            featureName: permissions[i].feature.name,
            actionName: permissions[i].action.name,
          } as UserRolePermission);
        }
      }

      await manager.save(UserRolePermission, preparedUserRolePermissions);
    }
  }

  async getAllRolePermissionByUser(userId: string, query: ListRolePermissionDto) {
    // const validCompaniesForRoles = [query.companyId];
    // if (query.parentCompanyId) {
    //   validCompaniesForRoles.push(query.parentCompanyId);
    // }
    const qb = this.createQueryBuilder('userRolePermission')
      .leftJoin('userRolePermission.role', 'role')
      .where('userRolePermission.userId = :userId', { userId })
      .select(
        // eslint-disable-next-line quotes
        "DISTINCT (userRolePermission.featureName || '::' || userRolePermission.actionName)",
        'permission',
      );

    // Check if user is in parent company
    if (!query.parentCompanyId) {
      qb.andWhere(
        '(role.companyId = :companyId OR role.companyId IS NULL) AND role.status = :status',
        {
          companyId: query.companyId,
          status: StatusCommon.ACTIVE,
        },
      );
    } else {
      if (query.switch) {
        qb.andWhere(
          '(role.companyId = :companyId OR (role.companyId is NULL AND role.isDefault = true)) AND role.status = :status',
          {
            companyId: query.companyId,
            status: StatusCommon.ACTIVE,
          },
        );
      } else {
        qb.andWhere(
          '(role.companyId = :companyId OR role.companyId = :parentCompanyId OR (role.companyId is NULL AND role.isDefault = true) ) AND role.status = :status',
          {
            companyId: query.companyId,
            parentCompanyId: query.parentCompanyId,
            status: StatusCommon.ACTIVE,
          },
        );
      }
    }

    return qb.getRawMany<{ permission: string }>(); // Don't need to sort
  }

  async getUserRolePermissionByUser(userId: string, query: ListRolePermissionDto) {
    const qb = this.createQueryBuilder('userRolePermission')
      .leftJoin('userRolePermission.role', 'role')
      .where('userRolePermission.userId = :userId', { userId })
      .select(
        // eslint-disable-next-line quotes
        `userRolePermission.featureName || \'::\' || userRolePermission.actionName`,
        `permission`,
      )
      .addSelect('role.name');

    // Check if user is in parent company
    if (!query.parentCompanyId) {
      qb.andWhere(
        '(role.companyId = :companyId OR role.companyId IS NULL) AND role.status = :status',
        {
          companyId: query.companyId,
          status: StatusCommon.ACTIVE,
        },
      );
    } else {
      if (query.switch) {
        qb.andWhere(
          '(role.companyId = :companyId OR (role.companyId is NULL AND role.isDefault = true)) AND role.status = :status',
          {
            companyId: query.companyId,
            status: StatusCommon.ACTIVE,
          },
        );
      } else {
        qb.andWhere(
          '(role.companyId = :companyId OR role.companyId = :parentCompanyId OR (role.companyId is NULL AND role.isDefault = true) ) AND role.status = :status',
          {
            companyId: query.companyId,
            parentCompanyId: query.parentCompanyId,
            status: StatusCommon.ACTIVE,
          },
        );
      }
    }

    return qb.getRawMany(); // Don't need to sort
  }
}
