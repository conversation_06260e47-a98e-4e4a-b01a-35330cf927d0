import {
  BadRequestError,
  BaseError,
  BaseMultiErrors,
  LoggerCommon,
  RoleScopeCheck,
  TokenPayloadModel,
  TypeORMRepository,
  Utils,
} from 'svm-nest-lib';
import { Connection, EntityRepository, In, <PERSON>Null, Not, Repository } from 'typeorm';
import { DBIndexes } from '../../../commons/consts/db.const';
import { DEFAULT_ROLES_INSPECTION_QA } from '../../../commons/consts/default-inspection-qa.const';
import { DEFAULT_ROLES_INSPECTION } from '../../../commons/consts/default-inspection.const';
import { DEFAULT_ROLES_QA } from '../../../commons/consts/default-qa.const';
import { ListQueryDto } from '../../../commons/dtos';
import {
  ActionEnum,
  CompanyLevelEnum,
  EntityTypePlanningRequest,
  FeatureEnum,
  PackageEnum,
  StatusCommon,
  SubFeatureEnum,
  WorkflowPermission,
  WorkflowStatus,
  WorkflowType,
} from '../../../commons/enums';
import { MySet } from '../../../utils';
import { IAMMetaConfig } from '../../commons/iam-meta-config/iam-meta-config.entity';
import { META_KEY } from '../../commons/iam-meta-config/meta-key.const';
import { ListUserAuditorsBodyDto, ListUserAuditorsQueryDto } from '../../work-flow/dto';
import * as ROLE_CONFIG_DATA from '../config/roles.config';
import { ROLE_NAME_DEFAULT } from '../config/roles.config';
import { ListRoleDto, ListRolePermissionDto, UpdateRoleDto } from '../dto';
import { CreateRoleDto } from '../dto/create-role.dto';
import { CreateUserRoleDto } from '../dto/create-user-role.dto';
import { Permission } from '../entities/permission.entity';
import { RolePermission } from '../entities/role-permission.entity';
import { Role } from '../entities/role.entity';
import { UserRolePermission } from '../entities/user-role-permission.entity';
import { UserRole } from '../entities/user-role.entity';
import { PermissionRepository } from './permission.repository';
import { UserRolePermissionRepository } from './user-role-permission.repository';
import { ListPRGraphicallyDTO } from 'src/modules/work-flow/dto/list-pr-group-graphical.dto';

@EntityRepository(Role)
export class RoleRepository extends TypeORMRepository<Role> {
  constructor(private readonly connection: Connection) {
    super();
  }

  private async _getPermissionIds(permissionsParam: { feature: string; actions: ActionEnum[] }[]) {
    const qb = this.manager
      .createQueryBuilder(Permission, 'permission')
      .leftJoin('permission.feature', 'feature')
      .leftJoin('permission.action', 'action')
      .select()
      .addSelect(['feature.name', 'action.name']);

    for (let i = 0; i < permissionsParam.length; i++) {
      qb.orWhere(`(feature.name = :feature${i} AND action.name IN (:...actions${i}))`, {
        [`feature${i}`]: permissionsParam[i].feature,
        [`actions${i}`]: permissionsParam[i].actions,
      });
    }

    const permissions = await qb.getMany();

    const permissionsMap = new Map<string, string>();
    for (let i = 0; i < permissions.length; i++) {
      const permissionKey = permissions[i].feature.name + '::' + permissions[i].action.name;
      permissionsMap.set(permissionKey, permissions[i].id);
    }
    return permissionsMap;
  }

  async _checkNeedToSyncDefaultRoles() {
    const version = await this.manager.findOne(IAMMetaConfig, {
      where: { key: META_KEY.DEFAULT_ROLE },
    });

    if (version && version.lastTimeSync === ROLE_CONFIG_DATA.VERSION) {
      return { flag: false, version };
    }

    return { flag: true, version };
  }

  async _checkNeedToSyncDefaultPackageRoles() {
    const version = await this.manager.findOne(IAMMetaConfig, {
      where: { key: META_KEY.DEFAULT_PACKAGE_ROLE },
    });

    if (version && version.lastTimeSync === ROLE_CONFIG_DATA.PACKAGE_ROLE_VERSION) {
      return { flag: false, version };
    }

    return { flag: true, version };
  }

  async _checkNeedToSyncPermission() {
    const version = await this.manager.findOne(IAMMetaConfig, {
      where: { key: META_KEY.ACTION_PERMISSION },
    });

    if (version && version.lastTimeSync === ROLE_CONFIG_DATA.ACTION_PERMISSION_VERSION) {
      return { flag: false, version };
    }

    return { flag: true, version };
  }

  async _checkInitDefaultRoleForSuperAdmin() {
    const check = await this.manager.findOne(IAMMetaConfig, {
      where: {
        key: META_KEY.MIGRATE_DEFAULT_ROLE,
      },
    });

    if (!check) {
      return true;
    }

    return false;
  }

  async _createDefaultRolesHelperForSuperAdmin() {
    try {
      const defaultRoles = DEFAULT_ROLES_INSPECTION_QA;
      const preparedRoles: Role[] = [];
      const preparedRolePermissions: RolePermission[] = [];

      const permissionsParam = defaultRoles.reduce(
        (rs: { feature: string; actions: ActionEnum[] }[], role) => {
          rs.push(...role.permissions);
          return rs;
        },
        [],
      );

      const permissionsMap = await this._getPermissionIds(permissionsParam);

      const defaultInspectionRoleNames = DEFAULT_ROLES_INSPECTION.map((role) => role.name);
      const defaultQARoleNames = DEFAULT_ROLES_QA.map((role) => role.name);

      // Loop default roles
      for (let r = 0; r < defaultRoles.length; r++) {
        const rawRole = {
          name: defaultRoles[r].name,
          description: defaultRoles[r].description,
          isDefault: true,
          isInspection: defaultInspectionRoleNames.includes(defaultRoles[r].name) ? true : false,
          isQA: defaultQARoleNames.includes(defaultRoles[r].name) ? true : false,
        };

        const rawPermissionIds: string[] = [];
        for (let p = 0; p < defaultRoles[r].permissions.length; p++) {
          for (let a = 0; a < defaultRoles[r].permissions[p].actions.length; a++) {
            const permissionKey =
              defaultRoles[r].permissions[p].feature +
              '::' +
              defaultRoles[r].permissions[p].actions[a];

            const permissionId = permissionsMap.get(permissionKey);
            if (permissionId) {
              rawPermissionIds.push(permissionId);
            }
          }
        }

        const roleId = Utils.strings.generateUUID();
        // Push role
        preparedRoles.push({
          id: roleId,
          ...rawRole,
          companyId: null,
        } as Role);
        // Push permissions of role
        preparedRolePermissions.push(
          ...rawPermissionIds.map((permissionId) => ({ roleId, permissionId } as RolePermission)),
        );
      }

      await this.manager.transaction(async (manager) => {
        await manager.save(Role, preparedRoles, { chunk: 100 });
        await manager.save(RolePermission, preparedRolePermissions, { chunk: 100 });
      });
      LoggerCommon.log(
        '[RoleRepository] _initDefaultRolesHelperForSuperAdmin done: ' +
          defaultRoles.length +
          ' new roles are created',
      );
    } catch (ex) {
      LoggerCommon.error('[RoleRepository] _initDefaultRolesForSuperAdmin', ex.message || ex);
      throw ex;
    }
  }

  async detailCompany(companyId: string) {
    const rawQuery = `SELECT * FROM company c where id = $1 AND deleted = false`;
    const rs = await this.connection.query(rawQuery, [companyId]);
    return rs[0];
  }

  async _createDefaultRolesHelper(
    isInit: boolean,
    companyIds: string[],
    version?: IAMMetaConfig,
    defaultRoles = DEFAULT_ROLES_INSPECTION_QA,
  ) {
    try {
      const preparedRoles: Role[] = [];
      const preparedRolePermissions: RolePermission[] = [];

      let newDefaultRoles = [];
      let existedDefaultRoles = [];

      // Check if need to filter only new roles
      if (version && version.lastTimeSync !== ROLE_CONFIG_DATA.VERSION) {
        const existedRoles = await this.createQueryBuilder('role')
          .where('role.name IN (:...names) AND role.deleted IS FALSE', {
            names: defaultRoles.map((role) => role.name),
          })
          .select(['DISTINCT role.name'])
          .getRawMany();

        const existedRoleNamesSet = new Set(existedRoles.map((role) => role.name));

        newDefaultRoles = defaultRoles.filter((role) => !existedRoleNamesSet.has(role.name));
        existedDefaultRoles = defaultRoles.filter((role) => existedRoleNamesSet.has(role.name));

        for (let r = 0; r < existedDefaultRoles.length; r++) {
          //Get all existed permissions from DEFAULT_ROLES
          const permissionsMap = await this._getPermissionIds(existedDefaultRoles[r].permissions);
          const existedPermissionIds: string[] = [];
          for (const value of permissionsMap.values()) {
            existedPermissionIds.push(value);
          }

          //Get all permission from existed default role
          const permissionsOfRole = await this.manager
            .createQueryBuilder(RolePermission, 'role_permission')
            .leftJoinAndSelect('role_permission.role', 'role')
            .leftJoinAndSelect('role_permission.permission', 'permission')
            .where('role.name = :name', {
              name: existedDefaultRoles[r].name,
            })
            .getMany();

          const rolePermissionIds: string[] = permissionsOfRole.map((por) => por.permissionId);
          const roleIds: string[] = [...new Set(permissionsOfRole.map((por) => por.roleId))];

          //Filter newly added permission
          const filteredNewPermissionIds: string[] = existedPermissionIds.filter(
            (permissionId) => !rolePermissionIds.includes(permissionId),
          );

          //Add role permission and user role permission for newly added permission
          if (filteredNewPermissionIds.length > 0) {
            const preparedNewRolePermission = [];
            const preparedUserRolePermission = [];

            const newPermissionInfos = await this.manager
              .createQueryBuilder(Permission, 'permission')
              .leftJoinAndSelect('permission.feature', 'feature')
              .leftJoinAndSelect('permission.action', 'action')
              .where('permission.id IN (:...permissionIds)', {
                permissionIds: filteredNewPermissionIds,
              })
              .getMany();

            for (const roleId of roleIds) {
              for (const permissionId of filteredNewPermissionIds) {
                preparedNewRolePermission.push({
                  roleId: roleId,
                  permissionId: permissionId,
                });
              }
            }

            const listAffectedUsers = await this.manager
              .createQueryBuilder(UserRole, 'user_role')
              .leftJoinAndSelect('user_role.role', 'role')
              .where('role.name = :name', {
                name: existedDefaultRoles[r].name,
              })
              .getMany();

            for (const user of listAffectedUsers) {
              for (const permission of newPermissionInfos) {
                preparedUserRolePermission.push({
                  userId: user.userId,
                  roleId: user.roleId,
                  permissionId: permission.id,
                  featureName: permission.feature.name,
                  actionName: permission.action.name,
                });
              }
            }

            await this.manager.transaction(async (manager) => {
              await manager.save(RolePermission, preparedNewRolePermission, { chunk: 10 });
              await manager.save(UserRolePermission, preparedUserRolePermission, { chunk: 10 });
              if (isInit) {
                if (version) {
                  await manager.update(
                    IAMMetaConfig,
                    { key: version.key },
                    { lastTimeSync: ROLE_CONFIG_DATA.VERSION },
                  );
                } else {
                  await manager.insert(IAMMetaConfig, {
                    key: META_KEY.DEFAULT_ROLE,
                    lastTimeSync: ROLE_CONFIG_DATA.VERSION,
                  });
                }
              }
            });
            LoggerCommon.log(
              '[RoleRepository] _initDefaultRolesHelper done: ' +
                preparedNewRolePermission.length +
                ' new roles permissions are created',
            );
          }
        }
      }

      if (!isInit && !version) {
        newDefaultRoles = defaultRoles;
      }

      if (newDefaultRoles.length === 0) {
        LoggerCommon.log(
          '[RoleRepository] _initDefaultRolesHelper done: there are no new roles need to create',
        );
        return;
      }

      const permissionsParam = newDefaultRoles.reduce(
        (rs: { feature: string; actions: ActionEnum[] }[], role) => {
          rs.push(...role.permissions);
          return rs;
        },
        [],
      );

      const permissionsMap = await this._getPermissionIds(permissionsParam);

      // Loop default roles
      for (let r = 0; r < newDefaultRoles.length; r++) {
        const rawRole = {
          name: defaultRoles[r].name,
          description: defaultRoles[r].description,
          isDefault: true,
        };

        const rawPermissionIds: string[] = [];
        for (let p = 0; p < newDefaultRoles[r].permissions.length; p++) {
          for (let a = 0; a < newDefaultRoles[r].permissions[p].actions.length; a++) {
            const permissionKey =
              newDefaultRoles[r].permissions[p].feature +
              '::' +
              newDefaultRoles[r].permissions[p].actions[a];

            const permissionId = permissionsMap.get(permissionKey);
            if (permissionId) {
              rawPermissionIds.push(permissionId);
            }
          }
        }

        // Loop All company to push role and its permissions
        for (let c = 0; c < companyIds.length; c++) {
          const roleId = Utils.strings.generateUUID();
          // Push role
          preparedRoles.push({
            id: roleId,
            ...rawRole,
            companyId: companyIds[c],
          } as Role);
          // Push permissions of role
          preparedRolePermissions.push(
            ...rawPermissionIds.map((permissionId) => ({ roleId, permissionId } as RolePermission)),
          );
        }
      }

      await this.manager.transaction(async (manager) => {
        await manager.save(Role, preparedRoles, { chunk: 100 });
        await manager.save(RolePermission, preparedRolePermissions, { chunk: 100 });
        //await manager.insert(Permission, preparedNewPermissions);
        if (isInit) {
          if (version) {
            await manager.update(
              IAMMetaConfig,
              { key: version.key },
              { lastTimeSync: ROLE_CONFIG_DATA.VERSION },
            );
          } else {
            await manager.insert(IAMMetaConfig, {
              key: META_KEY.DEFAULT_ROLE,
              lastTimeSync: ROLE_CONFIG_DATA.VERSION,
            });
          }
        }
      });
      LoggerCommon.log(
        '[RoleRepository] _initDefaultRolesHelper done: ' +
          newDefaultRoles.length +
          ' new roles are created',
      );
    } catch (ex) {
      LoggerCommon.error('[RoleRepository] _initDefaultRoles', ex.message || ex);
      throw ex;
    }
  }

  async _createDefaultPackageRolesHelper(isInit: boolean, version?: IAMMetaConfig) {
    try {
      const preparedRoles: Role[] = [];
      const preparedRolePermissions: RolePermission[] = [];
      // let preparedNewPermissions: Permission[] = [];

      const defaultPackageRoles = ROLE_CONFIG_DATA.DEFAULT_PACKAGE_ROLE;
      let newDefaultPackageRoles = [];
      let existedDefaultPackageRoles = [];

      // Check if need to filter only new roles
      if (version && version.lastTimeSync !== ROLE_CONFIG_DATA.PACKAGE_ROLE_VERSION) {
        const existedRoles = await this.createQueryBuilder('role')
          .where('role.name IN (:...names) AND role.deleted IS FALSE', {
            names: defaultPackageRoles.map((role) => role.name),
          })
          .select(['DISTINCT role.name'])
          .getRawMany();

        const existedRoleNamesSet = new Set(existedRoles.map((role) => role.name));

        newDefaultPackageRoles = defaultPackageRoles.filter(
          (role) => !existedRoleNamesSet.has(role.name),
        );
        existedDefaultPackageRoles = defaultPackageRoles.filter((role) =>
          existedRoleNamesSet.has(role.name),
        );

        for (let r = 0; r < existedDefaultPackageRoles.length; r++) {
          const features = existedDefaultPackageRoles[r].permissions.map((item) => item.feature);
          //Get all existed permissions from DEFAULT_ROLES
          const existedPermissions = await this.manager
            .createQueryBuilder(Permission, 'permission')
            .leftJoinAndSelect('permission.feature', 'feature')
            .leftJoinAndSelect('permission.action', 'action')
            .where('feature.name IN (:...features) AND permission.deleted IS FALSE', {
              features: features,
            })
            .getMany();

          const permissionsOfRole = await this.manager
            .createQueryBuilder(RolePermission, 'role_permission')
            .leftJoinAndSelect('role_permission.role', 'role')
            .leftJoinAndSelect('role_permission.permission', 'permission')
            .where('role.name = :name', {
              name: existedDefaultPackageRoles[r].name,
            })
            .getMany();

          const existedPermissionIds: string[] = existedPermissions.map((ep) => ep.id);
          const rolePermissionIds: string[] = permissionsOfRole.map((por) => por.permissionId);
          const roleIds: string[] = [...new Set(permissionsOfRole.map((por) => por.roleId))];

          const filteredNewPermissionIds: string[] = existedPermissionIds.filter(
            (permissionId) => !rolePermissionIds.includes(permissionId),
          );

          if (filteredNewPermissionIds.length > 0) {
            const preparedNewRolePermission = [];
            const preparedUserRolePermission = [];

            const newPermissionInfos = await this.manager
              .createQueryBuilder(Permission, 'permission')
              .leftJoinAndSelect('permission.feature', 'feature')
              .leftJoinAndSelect('permission.action', 'action')
              .where('permission.id IN (:...permissionIds)', {
                permissionIds: filteredNewPermissionIds,
              })
              .getMany();

            for (const roleId of roleIds) {
              for (const permissionId of filteredNewPermissionIds) {
                preparedNewRolePermission.push({
                  roleId: roleId,
                  permissionId: permissionId,
                });
              }
            }

            const listAffectedUsers = await this.manager
              .createQueryBuilder(UserRole, 'user_role')
              .leftJoinAndSelect('user_role.role', 'role')
              .where('role.name = :name', {
                name: defaultPackageRoles[r].name,
              })
              .getMany();

            for (const user of listAffectedUsers) {
              for (const permission of newPermissionInfos) {
                preparedUserRolePermission.push({
                  userId: user.userId,
                  roleId: user.roleId,
                  permissionId: permission.id,
                  featureName: permission.feature.name,
                  actionName: permission.action.name,
                });
              }
            }

            await this.manager.transaction(async (manager) => {
              await manager.save(RolePermission, preparedNewRolePermission, { chunk: 100 });
              await manager.save(UserRolePermission, preparedUserRolePermission, { chunk: 100 });
              if (isInit) {
                if (version) {
                  await manager.update(
                    IAMMetaConfig,
                    { key: version.key },
                    { lastTimeSync: ROLE_CONFIG_DATA.PACKAGE_ROLE_VERSION },
                  );
                } else {
                  await manager.insert(IAMMetaConfig, {
                    key: META_KEY.DEFAULT_ROLE,
                    lastTimeSync: ROLE_CONFIG_DATA.PACKAGE_ROLE_VERSION,
                  });
                }
              }
            });
            LoggerCommon.log(
              '[RoleRepository] _initDefaultPackageRolesHelper done: ' +
                preparedNewRolePermission.length +
                ' new roles permissions are created',
            );
          }
        }
      }

      if (newDefaultPackageRoles.length === 0) {
        LoggerCommon.log(
          '[RoleRepository] _initDefaultPackageRolesHelper done: there is no new roles need to create',
        );
        return;
      }

      const permissionsParam = newDefaultPackageRoles.reduce(
        (rs: { feature: string; actions: ActionEnum[] }[], role) => {
          rs.push(...role.permissions);
          return rs;
        },
        [],
      );

      const permissionsMap = await this._getPermissionIds(permissionsParam);

      // Loop default roles
      for (let r = 0; r < newDefaultPackageRoles.length; r++) {
        const rawRole = {
          name: newDefaultPackageRoles[r].name,
          description: newDefaultPackageRoles[r].description,
        };

        const rawPermissionIds: string[] = [];
        for (let p = 0; p < newDefaultPackageRoles[r].permissions.length; p++) {
          for (let a = 0; a < newDefaultPackageRoles[r].permissions[p].actions.length; a++) {
            const permissionKey =
              newDefaultPackageRoles[r].permissions[p].feature +
              '::' +
              newDefaultPackageRoles[r].permissions[p].actions[a];

            const permissionId = permissionsMap.get(permissionKey);
            if (permissionId) {
              rawPermissionIds.push(permissionId);
            }
          }
        }

        const roleId = Utils.strings.generateUUID();
        // Push role
        preparedRoles.push({
          id: roleId,
          ...rawRole,
          companyId: null,
        } as Role);
        // Push permissions of role
        preparedRolePermissions.push(
          ...rawPermissionIds.map((permissionId) => ({ roleId, permissionId } as RolePermission)),
        );
      }

      await this.manager.transaction(async (manager) => {
        await manager.insert(Role, preparedRoles);
        await manager.save(RolePermission, preparedRolePermissions, { chunk: 10 });
        //await manager.insert(Permission, preparedNewPermissions);
        if (isInit) {
          if (version) {
            await manager.update(
              IAMMetaConfig,
              { key: version.key },
              { lastTimeSync: ROLE_CONFIG_DATA.PACKAGE_ROLE_VERSION },
            );
          } else {
            await manager.insert(IAMMetaConfig, {
              key: META_KEY.DEFAULT_PACKAGE_ROLE,
              lastTimeSync: ROLE_CONFIG_DATA.PACKAGE_ROLE_VERSION,
            });
          }
        }
      });
      LoggerCommon.log(
        '[RoleRepository] _initDefaultPackageRolesHelper done: ' +
          newDefaultPackageRoles.length +
          ' new roles are created',
      );
    } catch (ex) {
      LoggerCommon.error('[RoleRepository] _initDefaultPackageRolesHelper', ex.message || ex);
      throw ex;
    }
  }

  async createRole(token: TokenPayloadModel, body: CreateRoleDto) {
    try {
      return await this.connection.transaction(async (manager) => {
        // Validate permissions
        const isValid = await manager
          .getCustomRepository(PermissionRepository)
          .validatePermissionIdsByUser(token, body.permissions);

        if (!isValid) {
          throw new BadRequestError({ message: 'role.PERMISSIONS_FOR_ROLE_NOT_ALLOW' });
        }

        const isInspectionPackage =
          token.roleScope === 'SuperAdmin' && body.rolePackage === PackageEnum.INSPECTIONS_PACKAGE;
        const isQAPackage =
          token.roleScope === 'SuperAdmin' && body.rolePackage === PackageEnum.QA_PACKAGE;
        const isInspectionAndQAPackage =
          token.roleScope === 'SuperAdmin' &&
          (!body.rolePackage || body.rolePackage === PackageEnum.INSPECTIONS_AND_QA_PACKAGE);

        const preparedRole = {
          name: body.name,
          description: body.description,
          status: body.status,
          companyId: token.roleScope === 'SuperAdmin' ? null : token.companyId,
        } as Role;

        if (token.roleScope === 'SuperAdmin') {
          Object.assign(preparedRole, { isDefault: true });
        }

        if (isInspectionAndQAPackage) {
          Object.assign(preparedRole, { isInspection: true, isQA: true });
        } else if (isInspectionPackage) {
          Object.assign(preparedRole, { isInspection: true, isQA: false });
        } else if (isQAPackage) {
          Object.assign(preparedRole, { isInspection: false, isQA: true });
        }

        // Insert into role table
        const newRole = await manager.save(Role, preparedRole);

        // Insert into role_permission table
        const rolePermissions: RolePermission[] = [];
        for (let i = 0; i < body.permissions.length; i++) {
          rolePermissions.push({
            roleId: newRole.id,
            permissionId: body.permissions[i],
          } as RolePermission);
        }
        await manager.save(RolePermission, rolePermissions, {
          chunk: 10000,
        });
        return {
          ...newRole,
          rolePermissions,
        };
      });
    } catch (ex) {
      LoggerCommon.error('[RoleRepository] error', ex.message || ex);
      if (
        [DBIndexes.IDX_ROLE_NAME_COMPANY_ID, DBIndexes.IDX_ROLE_NAME_COMPANY_ID_NULL].includes(
          ex.constraint,
        )
      ) {
        throw new BaseMultiErrors({
          status: 400,
          errors: [{ fieldName: 'name', message: 'role.NAME_EXISTED' }],
        });
      }
      throw ex;
    }
  }

  async listRoles(token: TokenPayloadModel, query: ListRoleDto) {
    let queryPackage = ``;
    if (!RoleScopeCheck.isSuperAdmin(token)) {
      const companyFound = await this.detailCompany(token.companyId);
      const isPackageQA = companyFound.isQA || null;
      const isPackageInspection = companyFound.isInspection || null;
      if (isPackageQA && !isPackageInspection) {
        queryPackage = ` AND role.isQA = TRUE`;
      }
      if (!isPackageQA && isPackageInspection) {
        queryPackage = ` AND role.isInspection = TRUE`;
      }
      if (isPackageQA && isPackageInspection) {
        queryPackage = ``;
      }
    }

    const queryBuilder = this.createQueryBuilder('role');

    if (RoleScopeCheck.isSuperAdmin(token)) {
      queryBuilder.andWhere('role.companyId IS NULL AND role.isDefault = TRUE');
    } else if (!token.parentCompanyId) {
      queryBuilder.andWhere(
        `(role.companyId = :companyId) OR (role.companyId IS NULL AND role.isDefault = TRUE ${queryPackage})`,
        {
          companyId: query.companyId || token.companyId,
        },
      );
    } else {
      queryBuilder.andWhere(
        `(role.companyId = :companyId) OR (role.companyId IS NULL AND role.isDefault = TRUE ${queryPackage})`,
        { companyId: token.companyId },
      );
    }

    if (query.content) {
      queryBuilder.andWhere('role.name ILIKE :content', {
        content: `%${query.content}%`,
      });
    }

    if (query.status) {
      queryBuilder.andWhere('role.status = :status', {
        status: query.status,
      });
    }

    if (query.isDefault) {
      queryBuilder
        .leftJoin('role.rolePermissions', 'rolePermissions')
        .andWhere('role.isDefault = TRUE')
        .select()
        .addSelect(['rolePermissions.id', 'rolePermissions.permissionId'])
        .orderBy('role.name');
    }

    const dataList = await this.list(
      { page: query.page, limit: query.pageSize },
      {
        queryBuilder,
        sort: query.sort || 'role.createdAt:-1',
      },
    );

    return dataList;
  }

  async listRolesForManageUser(companyIdOfCurrUser: string, query: ListQueryDto) {
    const queryBuilder = this.createQueryBuilder('role');

    if (!companyIdOfCurrUser) {
      queryBuilder.andWhere(`role.companyId IS NULL AND role.isDefault = TRUE`);
    } else {
      const companyFound = await this.detailCompany(companyIdOfCurrUser);
      const isPackageQA = companyFound.isQA || null;
      const isPackageInspection = companyFound.isInspection || null;
      let queryPackage = ``;
      if (isPackageQA && !isPackageInspection) {
        queryPackage = ` AND role.isQA = TRUE`;
      }
      if (!isPackageQA && isPackageInspection) {
        queryPackage = ` AND role.isInspection = TRUE`;
      }
      if (isPackageQA && isPackageInspection) {
        queryPackage = ``;
      }
      queryBuilder.andWhere(
        `(role.companyId = :companyId) OR (role.companyId IS NULL AND role.isDefault = TRUE ${queryPackage})`,
        { companyId: companyIdOfCurrUser },
      );
    }

    if (query.content) {
      queryBuilder.andWhere('role.name ILIKE :content', {
        content: `%${query.content}%`,
      });
    }

    if (query.status) {
      queryBuilder.andWhere('role.status = :status', {
        status: query.status,
      });
    }

    const dataList = await this.list(
      { page: query.page, limit: query.pageSize },
      {
        queryBuilder,
        sort: query.sort || 'role.createdAt:1',
      },
    );

    return dataList;
  }

  async getDefaultRoles(companyIdOfCurrUser: string) {
    const qb = this.createQueryBuilder('role').where('role.name IN (:...defaultRoles)', {
      defaultRoles: [
        ROLE_CONFIG_DATA.FIXED_ROLE_NAME.INSPECTOR,
        ROLE_CONFIG_DATA.FIXED_ROLE_NAME.AUDITEE,
      ],
    });

    if (!companyIdOfCurrUser) {
      qb.andWhere('role.companyId IS NULL');
    } else {
      qb.andWhere('role.companyId = :companyId', { companyId: companyIdOfCurrUser });
    }

    // const qb = this.createQueryBuilder('role').where('role.isDefault = TRUE');

    return qb.orderBy('role.createdAt', 'ASC').getMany();
  }

  async getDetailRole(id: string, repositoryRole?: Repository<Role>) {
    return (repositoryRole || this).findOne({
      where: {
        id,
        //companyId: companyIdOfCurrUser || IsNull(),
      },
      relations: ['rolePermissions'],
    });
  }

  async updateRole(token: TokenPayloadModel, roleId: string, body: UpdateRoleDto) {
    try {
      return await this.connection.transaction(async (manager) => {
        // Validate permissions
        const isValid = manager
          .getCustomRepository(PermissionRepository)
          .validatePermissionIdsByUser(token, body.permissions);

        if (!isValid) {
          throw new BadRequestError({ message: 'role.PERMISSIONS_FOR_ROLE_NOT_ALLOW' });
        }

        const roleFound = await manager.findOne(Role, { id: roleId });
        if (roleFound.isDefault && token.roleScope !== 'SuperAdmin') {
          throw new BaseError({ message: 'role.CANNOT_UPDATE_DEFAULT_ROLES_NAME' });
        }

        const isInspectionPackage =
          token.roleScope === 'SuperAdmin' && body.rolePackage === PackageEnum.INSPECTIONS_PACKAGE;
        const isQAPackage =
          token.roleScope === 'SuperAdmin' && body.rolePackage === PackageEnum.QA_PACKAGE;
        const isInspectionAndQAPackage =
          token.roleScope === 'SuperAdmin' &&
          (!body.rolePackage || body.rolePackage === PackageEnum.INSPECTIONS_AND_QA_PACKAGE);

        const preparedUpdateRole = {
          name: body.name,
          description: body.description,
          status: body.status,
        };

        if (isInspectionAndQAPackage) {
          Object.assign(preparedUpdateRole, { isInspection: true, isQA: true });
        } else if (isInspectionPackage) {
          Object.assign(preparedUpdateRole, { isInspection: true, isQA: false });
        } else if (isQAPackage) {
          Object.assign(preparedUpdateRole, { isInspection: false, isQA: true });
        }

        // Update role table
        const updateResult = await manager.update(
          Role,
          {
            id: roleId,
            companyId: token.roleScope === 'SuperAdmin' ? IsNull() : token.companyId,
            // name: Not(
            //   In([
            //     ROLE_CONFIG_DATA.FIXED_ROLE_NAME.INSPECTOR,
            //     ROLE_CONFIG_DATA.FIXED_ROLE_NAME.AUDITEE,
            //     ROLE_CONFIG_DATA.FIXED_PACKAGE_ROLE_NAME.INAUTIX_INSPECTIONS_AND_QA_PACKAGE_ROLES,
            //     ROLE_CONFIG_DATA.FIXED_PACKAGE_ROLE_NAME.INAUTIX_INSPECTIONS_PACKAGE_ROLES,
            //     ROLE_CONFIG_DATA.FIXED_PACKAGE_ROLE_NAME.INAUTIX_QA_PACKAGE_ROLES,
            //   ]),
            // ),
          },
          preparedUpdateRole,
        );

        if (updateResult.affected == 0) {
          throw new BaseError({ status: 404, message: 'role.NOT_FOUND' });
        }

        // Update role permission table
        //- Find all current enable permission ids of Role
        const currentPermissionIds = new Set<string>();
        (
          await manager.find(RolePermission, {
            where: { roleId },
            select: ['permissionId'],
          })
        ).forEach((item) => currentPermissionIds.add(item.permissionId));

        //- Transform input permissions array to Set
        const inputPermissionIds = new Set<string>(body.permissions);

        //- Find all permission ids needed remove from role.
        const removePermissionIds = MySet.difference(currentPermissionIds, inputPermissionIds);
        if (removePermissionIds.size > 0) {
          await manager.delete(RolePermission, {
            roleId,
            permissionId: In([...removePermissionIds]),
          });
        }
        //- Find all permission ids needed to add to role
        const newPermissionIds = MySet.difference(inputPermissionIds, currentPermissionIds);
        if (newPermissionIds.size > 0) {
          await manager.save(
            RolePermission,
            [...newPermissionIds].map((x) => ({ roleId, permissionId: x })) as RolePermission[],
          );
        }

        // Update Cache permission in user to user_role_permission table
        await manager
          .getCustomRepository(UserRolePermissionRepository)
          ._updateWhenRoleChangePermission(
            manager,
            roleId,
            [...removePermissionIds],
            [...newPermissionIds],
          );
      });
    } catch (ex) {
      LoggerCommon.error('[RoleRepository] error', ex.message || ex);
      if (
        [DBIndexes.IDX_ROLE_NAME_COMPANY_ID, DBIndexes.IDX_ROLE_NAME_COMPANY_ID_NULL].includes(
          ex.constraint,
        )
      ) {
        throw new BaseMultiErrors({
          status: 400,
          errors: [{ fieldName: 'name', message: 'role.NAME_EXISTED' }],
        });
      }
      throw ex;
    }
  }

  async deleteRole(token: TokenPayloadModel, roleId: string) {
    const roleFound = await this.manager.findOne(Role, { id: roleId });
    if (roleFound.isDefault && token.roleScope !== 'SuperAdmin') {
      throw new BaseError({ message: 'role.CANNOT_UPDATE_DEFAULT_ROLES_NAME' });
    }

    // Hard delete
    const deleteResult = await this.delete({
      id: roleId,
      companyId: token.roleScope === 'SuperAdmin' ? IsNull() : token.companyId,
      // name: Not(
      //   In([
      //     ROLE_CONFIG_DATA.FIXED_ROLE_NAME.INSPECTOR,
      //     ROLE_CONFIG_DATA.FIXED_ROLE_NAME.AUDITEE,
      //     ROLE_CONFIG_DATA.FIXED_PACKAGE_ROLE_NAME.INAUTIX_INSPECTIONS_AND_QA_PACKAGE_ROLES,
      //     ROLE_CONFIG_DATA.FIXED_PACKAGE_ROLE_NAME.INAUTIX_INSPECTIONS_PACKAGE_ROLES,
      //     ROLE_CONFIG_DATA.FIXED_PACKAGE_ROLE_NAME.INAUTIX_QA_PACKAGE_ROLES,
      //   ]),
      // ),
    });
    if (deleteResult.affected === 0) {
      throw new BaseError({ status: 404, message: 'role.NOT_FOUND_OR_CANNOT_DELETE_DEFAULT_ROLE' });
    }
    return deleteResult.affected;
  }

  async getAllRoleByUser(userId: string) {
    return this.createQueryBuilder('role')
      .leftJoin('role.userRoles', 'userRole')
      .where('userRole.userId = :userId', {
        userId,
      })
      .orderBy('role.createdAt', 'DESC')
      .getMany();
  }

  async listWorkflowPermissionByUser(userId: string, query: ListRolePermissionDto) {
    // const validCompaniesForWorkflow = [query.companyId];
    // if (query.parentCompanyId) {
    //   validCompaniesForWorkflow.push(query.parentCompanyId);
    // }

    const queryBuilder = this.createQueryBuilder('role')
      .leftJoin('role.userRoles', 'userRoles')
      .leftJoin('role.workflowRoles', 'workflowRoles')
      .leftJoin('workflowRoles.workflow', 'workflow')
      .where(
        `userRoles.userId = :userId AND 
        workflow.companyId = :companyId AND
        workflow.workflowType = :workflowType AND 
        workflow.status = :status AND
        workflow.deleted = FALSE`, // Maybe need to add filter workflow by company later
        {
          userId,
          companyId: query.companyId,
          workflowType: query.workflowType,
          status: WorkflowStatus.PUBLISHED,
        },
      )
      .select(['role.id'])
      .addSelect(['workflowRoles.id', 'workflowRoles.permission']);

    return this.getManyQB(queryBuilder);
  }
}

@EntityRepository(RolePermission)
export class RolePermissionRepository extends TypeORMRepository<RolePermission> {}

@EntityRepository(UserRole)
export class UserRoleRepository extends TypeORMRepository<UserRole> {
  constructor(private readonly connection: Connection) {
    super();
  }

  async createUserRole(userId: string, body: CreateUserRoleDto) {
    // Check input roleIs are valid: is exists, active
    const validRoles = await this.manager.getRepository(Role).find({
      where: {
        id: In(body.roleIds),
        status: StatusCommon.ACTIVE,
      }, // Create new user with only active roles
      select: ['id'],
    });

    if (validRoles.length !== body.roleIds.length) {
      throw new BaseError({ status: 400, message: 'role.SOME_ROLES_INVALID' });
    }

    await this.manager.transaction(async (managerTrans) => {
      // Save to user_role
      await managerTrans.save(
        UserRole,
        body.roleIds.map((x) => ({ userId, roleId: x })),
        {
          chunk: 10000,
        },
      );
      // Cache permission of user in user_role_permission table
      await managerTrans
        .getCustomRepository(UserRolePermissionRepository)
        ._createUserRolePermission(managerTrans, userId, body.roleIds);
    });
  }

  async updateUserRole(userId: string, body: CreateUserRoleDto) {
    try {
      // Check input roleIs are existed
      const validRoles = await this.manager.getRepository(Role).find({
        where: { id: In(body.roleIds) }, // update user with both active and inactive roles
        select: ['id'],
      });
      if (validRoles.length !== body.roleIds.length) {
        throw new BaseError({ status: 400, message: 'role.SOME_ROLES_INVALID' });
      }

      return await this.connection.transaction(async (manager) => {
        // Update user role table
        //- Find all current role ids of user
        const currentRoleIds = new Set<string>();
        (
          await manager.find(UserRole, {
            where: { userId },
            select: ['roleId'],
          })
        ).forEach((item) => currentRoleIds.add(item.roleId));

        //- Transform input user's roleIds array to Set
        const inputRoleIds = new Set<string>(body.roleIds);

        //- Find all role ids needed remove from user.
        const removeRoleIds = MySet.difference(currentRoleIds, inputRoleIds);

        let isRemovedRoleInspector = false;
        let updatedRoles = [];
        if (removeRoleIds.size > 0) {
          // Check if one of removed roles is Inspector
          const counter = await manager
            .createQueryBuilder(Role, 'role')
            .where('role.id IN (:...removeRoleIds) AND role.name = :roleName', {
              removeRoleIds: Array.from(removeRoleIds),
              roleName: ROLE_CONFIG_DATA.FIXED_ROLE_NAME.INSPECTOR,
            })
            .select(['count(*) AS count'])
            .getRawOne();

          isRemovedRoleInspector = counter && counter.count > 0;

          const removed = await manager.delete(UserRole, {
            userId,
            roleId: In([...removeRoleIds]),
          });
          // if removed exists roles
          if (removed.affected) {
            updatedRoles = await this.manager.getRepository(Role).find({
              where: { id: In(body.roleIds.map((x) => x)) },
            });
          }
        }
        //- Find all role ids needed to add to user
        const newRoleIds = MySet.difference(inputRoleIds, currentRoleIds);
        if (newRoleIds.size > 0) {
          const newRoles = await manager.save(
            UserRole,
            [...newRoleIds].map((x) => ({ userId, roleId: x })) as UserRole[],
          );
          // if added new roles
          if (newRoles.length > 0) {
            updatedRoles = await this.manager.getRepository(Role).find({
              where: { id: In(body.roleIds.map((x) => x)) },
            });
          }
        }

        // Update Cache permission in user to user_role_permission table
        await manager
          .getCustomRepository(UserRolePermissionRepository)
          ._updateWhenUserChangeRole(manager, userId, [...removeRoleIds], [...newRoleIds]);

        return { isRemovedRoleInspector, updatedRoles };
      });
    } catch (ex) {
      LoggerCommon.error('[UserRoleRepository] updateUserRole error', ex.message || ex);
      throw ex;
    }
  }

  async checkRoleUpdateInspector(userId: string, body: CreateUserRoleDto) {
    try {
      // Check input roleIs are existed
      const validRoles = await this.manager.getRepository(Role).find({
        where: { id: In(body.roleIds) }, // update user with both active and inactive roles
        select: ['id'],
      });
      if (validRoles.length !== body.roleIds.length) {
        throw new BaseError({ status: 400, message: 'role.SOME_ROLES_INVALID' });
      }

      return await this.connection.transaction(async (manager) => {
        // Update user role table
        //- Find all current role ids of user
        const currentRoleIds = new Set<string>();
        (
          await manager.find(UserRole, {
            where: { userId },
            select: ['roleId'],
          })
        ).forEach((item) => currentRoleIds.add(item.roleId));

        //- Transform input user's roleIds array to Set
        const inputRoleIds = new Set<string>(body.roleIds);

        //- Find all role ids needed remove from user.
        const removeRoleIds = MySet.difference(currentRoleIds, inputRoleIds);

        let isRemovedRoleInspector = false;
        if (removeRoleIds.size > 0) {
          // Check if one of removed roles is Inspector
          const counter = await manager
            .createQueryBuilder(Role, 'role')
            .where('role.id IN (:...removeRoleIds) AND role.name = :roleName', {
              removeRoleIds: Array.from(removeRoleIds),
              roleName: ROLE_CONFIG_DATA.FIXED_ROLE_NAME.INSPECTOR,
            })
            .select(['count(*) AS count'])
            .getRawOne();

          isRemovedRoleInspector = counter && counter.count > 0;
        }
        return isRemovedRoleInspector;
      });
    } catch (ex) {
      LoggerCommon.error('[UserRoleRepository] checkRoleUpdateInpector error', ex.message || ex);
      throw ex;
    }
  }

  async listUserAuditorsPR(user: TokenPayloadModel, query?: ListUserAuditorsQueryDto) {
    const queryBuilder = this.createQueryBuilder('userRoles')
      .leftJoin('userRoles.role', 'role')
      .leftJoin('role.workflowRoles', 'workflowRoles')
      .leftJoinAndSelect('workflowRoles.workflow', 'workflow')
      .where(
        `workflow.workflowType = :workflowType AND 
        workflow.status = :status AND 
        (workflow.companyId = :companyId or workflow.companyId = :explicitCompanyId ) AND 
        workflowRoles.permission = :permission`,
        {
          workflowType: WorkflowType.PLANNING_REQUEST,
          status: WorkflowStatus.PUBLISHED,
          companyId: user.companyId,
          explicitCompanyId: user.explicitCompanyId,
          permission: WorkflowPermission.AUDITOR,
        },
      )
      .select(['DISTINCT userRoles.userId "userId"']);

    return this.getRawManyQB<UserRole>(queryBuilder);
  }

  async listUserAuditorsPRAddFilter(user: TokenPayloadModel, query?: ListUserAuditorsBodyDto) {
    const queryBuilder = this.createQueryBuilder('userRoles').leftJoin('userRoles.role', 'role');
    const conditionQuery = `(role.companyId = '${user.companyId}' OR (role.companyId IS NULL AND role.isDefault = TRUE))`;
    if (query?.entityType === EntityTypePlanningRequest.OFFICE) {
      if(query?.isSA){
        queryBuilder.leftJoin('role.rolePermissions', 'rolePermissions')
        .leftJoin('rolePermissions.permission', 'permission')
        .leftJoin('permission.feature', 'feature');
        queryBuilder.andWhere('feature.name =:name',{
          name: `${FeatureEnum.QUALITY_ASSURANCE_SELF_ASSESSMENT}::${SubFeatureEnum.SELF_ASSESSMENT}`
        });
      }
      if (user.companyLevel === CompanyLevelEnum.MAIN_COMPANY) {
        queryBuilder.andWhere(`${conditionQuery}`);
      } else {
        queryBuilder.andWhere(`(${conditionQuery} and role.name IN(:...roleNames) )`, {
          roleNames: [
            ROLE_CONFIG_DATA.FIXED_ROLE_NAME.INSPECTOR,
            ROLE_NAME_DEFAULT.INSPECTION.INTERNAL_INSPECTOR,
            ROLE_NAME_DEFAULT.INSPECTION.EXTERNAL_INSPECTOR,
          ],
        });
      }
    } else {
      // queryBuilder
      //   .leftJoin('role.workflowRoles', 'workflowRoles')
      //   .leftJoinAndSelect('workflowRoles.workflow', 'workflow')
      //   .where(
      //     `workflow.workflowType = :workflowType AND
      //   workflow.status = :status AND
      //   workflowRoles.permission = :permission`,
      //     {
      //       workflowType: WorkflowType.PLANNING_REQUEST,
      //       status: WorkflowStatus.PUBLISHED,
      //       permission: WorkflowPermission.AUDITOR,
      //     },
      //   )
      //   .andWhere('(workflow.companyId = :companyId or workflow.companyId = :explicitCompanyId )', {
      //     companyId: user.companyId,
      //     explicitCompanyId: user.explicitCompanyId,
      //   });
      queryBuilder.andWhere(`(${conditionQuery} and role.name IN(:...roleNames) )`, {
        roleNames: [
          ROLE_CONFIG_DATA.FIXED_ROLE_NAME.INSPECTOR,
          ROLE_NAME_DEFAULT.INSPECTION.INTERNAL_INSPECTOR,
          ROLE_NAME_DEFAULT.INSPECTION.EXTERNAL_INSPECTOR,
        ],
      });
    }
    queryBuilder.select(['DISTINCT userRoles.userId "userId"']);

    return this.getRawManyQB<UserRole>(queryBuilder);
  }

  async listUserAuditorsInCompanies(companyId: string) {
    const queryBuilder = this.createQueryBuilder('userRoles')
      .leftJoin('userRoles.role', 'role')
      .leftJoin('role.workflowRoles', 'workflowRoles')
      .leftJoinAndSelect('workflowRoles.workflow', 'workflow')
      .where(
        `workflow.workflowType = :workflowType AND 
        workflow.status = :status AND 
        workflow.companyId = :companyId AND 
        workflowRoles.permission = :permission`,
        {
          workflowType: WorkflowType.PLANNING_REQUEST,
          status: WorkflowStatus.PUBLISHED,
          companyId,
          permission: WorkflowPermission.APPROVER,
        },
      )
      .select(['DISTINCT userRoles.userId "userId"']);

    return this.getRawManyQB<UserRole>(queryBuilder);
  }

  async listAuditors(companyId: string, query: ListPRGraphicallyDTO) {
    const roleNames = [
      `'${ROLE_CONFIG_DATA.FIXED_ROLE_NAME.INSPECTOR}'`,
      `'${ROLE_NAME_DEFAULT.INSPECTION.INTERNAL_INSPECTOR}'`,
      `'${ROLE_NAME_DEFAULT.INSPECTION.EXTERNAL_INSPECTOR}'`,
    ];
    let queryCondition = '';
    if (query.auditorName) {
      queryCondition = `AND u."username" ILIKE '%${query.auditorName}%'`;
    }
    const rawQuery = `SELECT u.id, u.username, u."companyId"  FROM "user" u
                      LEFT JOIN "user_role" ur ON ur."userId" = u.id
                      LEFT JOIN "role" r ON ur."roleId" = r.id
                      WHERE (r."companyId"  = $1 OR (r."companyId" is null and r."isDefault"= true))
                      AND r."name" IN (${roleNames})
                      ${queryCondition}
                      AND (u."companyId" = $1 OR u."parentCompanyId" = $1) and u.deleted = false;`;
    const rs = await this.connection.query(rawQuery, [companyId]);

    const uniqueAuditors = Array.from(new Set(rs.map((a) => a.id))).map((id) => {
      return rs.find((auditor) => auditor.id === id);
    });

    return uniqueAuditors;
  }

  async _migrateUserDefaultRole() {
    const defaultRoleNames = DEFAULT_ROLES_INSPECTION_QA.map((role) => role.name);
    const defaultRoles = await this.manager.find(Role, {
      where: {
        name: In(defaultRoleNames),
        companyId: null,
        isDefault: true,
      },
    });

    if (defaultRoles.length > 0) {
      const listAffectedUsers = await this.manager
        .createQueryBuilder(UserRole, 'user_role')
        .leftJoinAndSelect('user_role.role', 'role')
        .where('role.name IN(:...names)', {
          names: defaultRoles.map((role) => role.name),
        })
        .getMany();

      const preparedUserRole = [];
      for (const user of listAffectedUsers) {
        for (const role of defaultRoles) {
          if (user.role.name === role.name) {
            preparedUserRole.push({
              userId: user.userId,
              roleId: role.id,
            });
          }
        }
      }

      const permissionsOfRole = await this.manager
        .createQueryBuilder(RolePermission, 'role_permission')
        .leftJoinAndSelect('role_permission.role', 'role')
        .leftJoinAndSelect('role_permission.permission', 'permission')
        .leftJoinAndSelect('permission.feature', 'feature')
        .leftJoinAndSelect('permission.action', 'action')
        .where('role.name IN(:...names)', {
          names: defaultRoles.map((role) => role.name),
        })
        .andWhere('(role.isDefault = TRUE AND role.companyId IS NULL)')
        .getMany();

      const preparedUserRolePermission = [];
      for (const user of preparedUserRole) {
        for (const data of permissionsOfRole) {
          if (user.roleId === data.roleId) {
            preparedUserRolePermission.push({
              userId: user.userId,
              roleId: user.roleId,
              permissionId: data.permissionId,
              featureName: data.permission.feature.name,
              actionName: data.permission.action.name,
            });
          }
        }
      }

      await this.manager.transaction(async (manager) => {
        await manager.save(UserRole, preparedUserRole, { chunk: 10 });
        await manager.save(UserRolePermission, preparedUserRolePermission, { chunk: 10 });
        //delete old default role
        await manager.delete(Role, {
          name: In(defaultRoles.map((role) => role.name)),
          isDefault: true,
          companyId: Not(IsNull()),
        });
        await manager.insert(IAMMetaConfig, {
          key: META_KEY.MIGRATE_DEFAULT_ROLE,
          lastTimeSync: new Date().toISOString(),
        });
      });
      LoggerCommon.log(
        '[UserRoleRepository] _migrateUserDefaultRole done: ' +
          preparedUserRole.length +
          'user role created and ' +
          preparedUserRolePermission.length +
          'user role permission created',
      );
    }
  }

  async listUserRoleByUserIds(userIds: string[]) {
    const userRoles = this.createQueryBuilder('userRoles')
      .leftJoin('userRoles.role', 'role')
      .where('userRoles.userId IN (:...userIds) AND role.name NOT IN (:...packageRoles)', {
        userIds: userIds,
        packageRoles: [
          ROLE_CONFIG_DATA.FIXED_PACKAGE_ROLE_NAME.INAUTIX_INSPECTIONS_AND_QA_PACKAGE_ROLES,
          ROLE_CONFIG_DATA.FIXED_PACKAGE_ROLE_NAME.INAUTIX_INSPECTIONS_PACKAGE_ROLES,
          ROLE_CONFIG_DATA.FIXED_PACKAGE_ROLE_NAME.INAUTIX_QA_PACKAGE_ROLES,
        ],
      })
      .select(['userRoles.userId', 'role.name'])
      .getMany();
    return userRoles;
  }
}

/**
 * Data format
 * (1):
 [
    {
        "id": "0c29b118-cf62-4135-8443-222b2e006622",
        "feature": {
            "name": "user management"
        },
        "action": {
            "name": "view"
        },
        "rolePermissions": [
            {
                "roleId": "3cdcda02-818d-4a0a-a815-9b41bc2bdb74"
            }
        ]
    },
    {
        "id": "fda1d4c9-0955-42ec-ad93-d3077ea45da1",
        "feature": {
            "name": "role mangement"
        },
        "action": {
            "name": "create"
        },
        "rolePermissions": [
            {
                "roleId": "d4b33815-8552-4c23-a94f-857768af3ff3"
            },
            {
                "roleId": "3cdcda02-818d-4a0a-a815-9b41bc2bdb74"
            }
        ]
    }
  ]
 */
