import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  TokenPayloadModel,
  TypeORMRepository,
  Utils,
} from 'svm-nest-lib';
import { Connection, EntityRepository, In, Like } from 'typeorm';
import { ListQueryDto } from '../../../commons/dtos';
import { ActionEnum, FeatureEnum, SubFeatureEnum } from '../../../commons/enums';
import { IAMMetaConfig } from '../../commons/iam-meta-config/iam-meta-config.entity';
import { META_KEY } from '../../commons/iam-meta-config/meta-key.const';
import { ACTIONS_CONFIG, FEATURES_CONFIG, IFeature } from '../config';
import * as ROLE_CONFIG_DATA from '../config/roles.config';
import * as FEATURE_CONFIG_DATA from '../config/features.config';
import { Action } from '../entities/action.entity';
import { Feature } from '../entities/feature.entity';
import { Permission } from '../entities/permission.entity';
import { omit } from 'lodash';
import * as FEATURES_CONFIG_DATA from '../config/features.config';

@EntityRepository(Feature)
export class FeatureRepository extends TypeORMRepository<Feature> {
  constructor(private readonly connection: Connection) {
    super();
  }

  listFeatures(query: ListQueryDto) {
    const conditions: any = {};
    if (query.content) {
      conditions.name = Like(`%${query.content}%`);
    }
    return this.list(
      { page: query.page, limit: query.pageSize },
      { sort: query.sort || 'createdAt:1' },
      conditions,
    );
  }

  async listPermissionsGroupByFeatures(query: ListQueryDto, token: TokenPayloadModel) {
    const queryBuilder = this.createQueryBuilder('feature')
      .leftJoin('feature.permissions', 'permission')
      .leftJoinAndSelect('permission.action', 'action')
      .leftJoinAndSelect('feature.parent', 'parent')
      .select([
        'feature.id',
        'feature.name',
        'feature.description',
        'feature.order',
        'feature.createdAt',
        'feature.isLeaf',
        'permission.id',
        'action.id',
        'action.name',
        'action.createdAt',
        'parent.id',
      ]);

    if (!RoleScopeCheck.isSuperAdmin(token)) {
      queryBuilder
        .leftJoin('permission.rolePermissions', 'rolePermissions')
        .leftJoin('rolePermissions.role', 'role')
        .leftJoin('role.userRoles', 'userRoles')
        .where('(feature.isLeaf = FALSE OR userRoles.userId = :userId)', { userId: token.id });
    }

    if (query.content) {
      queryBuilder.andWhere('feature.name LIKE :content', {
        content: query.content,
      });
    }

    const dataList = await this.list(
      { page: query.page, limit: query.pageSize },
      {
        queryBuilder,
        sort: query.sort || 'feature.order:1;feature.description:1;action.createdAt:1',
      },
    );

    return dataList;
  }

  async _checkNeedToSyncFeature() {
    const version = await this.manager.findOne(IAMMetaConfig, {
      where: { key: META_KEY.FEATURE_PERMISSION },
    });

    if (version && version.lastTimeSync === FEATURES_CONFIG_DATA.FEATURE_SYNC_VERSION) {
      return { flag: false, version };
    }

    return { flag: true, version };
  }

  _getCurrentFeatureConfigNames(features: IFeature[], name?: string) {
    let featureNames: string[] = [];
    for (let i = 0; i < features.length; i++) {
      const subFeatures = features[i].subFeatures;
      const featureName = !name ? features[i].name : name + '::' + features[i].name;

      if (!subFeatures || subFeatures.length === 0) {
        featureNames.push(featureName);
      } else {
        featureNames.push(featureName);
        featureNames = featureNames.concat(
          this._getCurrentFeatureConfigNames(subFeatures, featureName),
        );
      }
    }
    return featureNames;
  }

  async _updateFeaturesHelper(features: IFeature[], parentId?: string, parentFeatureName?: string) {
    if (!features) {
      return;
    }
    const currentFeatureNames = this._getCurrentFeatureConfigNames(FEATURES_CONFIG);
    // find existed feature in db
    const existedFeatures = await this.manager.find(Feature);
    const existedFeatureNames = existedFeatures.map((feature) => feature.name);
    // filter to get the difference between db and config
    const featureDifference = existedFeatureNames.filter(
      (name) => !currentFeatureNames.includes(name),
    );
    for (let i = 0; i < features.length; i++) {
      const featureId = Utils.strings.generateUUID();
      const subFeatures = features[i].subFeatures;
      const actions = features[i].actions;

      const preparedFeature = {
        id: featureId,
        name: !parentFeatureName ? features[i].name : parentFeatureName + '::' + features[i].name,
        description: features[i].description || features[i].name,
        parentId: parentId ? parentId : null,
        order: features[i].order,
        isLeaf: features[i].isLeaf,
      } as Feature;

      // case insert new feature
      if (!existedFeatureNames.includes(preparedFeature.name) && featureDifference.length === 0) {
        const existedFeature = await this.manager.findOne(Feature, {
          where: {
            name: parentFeatureName,
          },
        });
        if (existedFeature) {
          Object.assign(preparedFeature, { parentId: existedFeature.id });
        }
        await this.manager.save(Feature, preparedFeature);
        if (actions) {
          const actionFound = await this.manager.find(Action, {
            where: {
              name: In(actions),
            },
          });
          for (let af = 0; af < actionFound.length; af++) {
            const preparedPermission = {
              featureId: preparedFeature.id,
              actionId: actionFound[af].id,
            } as Permission;

            await this.manager.save(Permission, preparedPermission);
          }
        }
      }
      // case edit feature info
      else if (
        existedFeatureNames.includes(preparedFeature.name) &&
        featureDifference.length === 0
      ) {
        const featureFound = await this.manager.findOne(Feature, {
          where: {
            name: preparedFeature.name,
          },
        });
        await this.manager.update(
          Feature,
          { id: featureFound.id },
          omit(preparedFeature, ['id', 'parentId']),
        );
      }
      // case delete feature in db when a feature in config is deleted
      else if (featureDifference.length > 0) {
        await this.manager.delete(Feature, { name: In(featureDifference) });
      }

      this._updateFeaturesHelper(subFeatures, featureId, preparedFeature.name);
    }
  }

  async _initFeaturesPermissions(isInit: boolean, version: IAMMetaConfig) {
    try {
      await this.manager.transaction(async (manager) => {
        await this._updateFeaturesHelper(FEATURES_CONFIG);
        if (isInit) {
          if (version) {
            await manager.update(
              IAMMetaConfig,
              { key: version.key },
              { lastTimeSync: FEATURE_CONFIG_DATA.FEATURE_SYNC_VERSION },
            );
          } else {
            await manager.insert(IAMMetaConfig, {
              key: META_KEY.FEATURE_PERMISSION,
              lastTimeSync: FEATURE_CONFIG_DATA.FEATURE_SYNC_VERSION,
            });
          }
        }
      });
      LoggerCommon.log('[FeatureRepository] _initFeaturesPermission done');
    } catch (ex) {
      LoggerCommon.error('[FeatureRepository] _initFeaturesPermission', ex.message || ex);
      throw ex;
    }
  }
}

@EntityRepository(Action)
export class ActionRepository extends TypeORMRepository<Action> {
  listAllActions() {
    return this.find({ order: { createdAt: 1 } });
  }

  async _initActions() {
    const count = await this.count();
    if (count > 0) return;

    const actionNames = Object.keys(ACTIONS_CONFIG);
    return this.insert(
      actionNames.map((action) => ({ id: Utils.strings.generateUUID(), name: action } as Action)),
    );
  }
}

@EntityRepository(Permission)
export class PermissionRepository extends TypeORMRepository<Permission> {
  constructor(private readonly connection: Connection) {
    super();
  }
  async listPermissionIdsByUser(token: TokenPayloadModel) {
    const queryBuilder = this.createQueryBuilder('permission').select(['DISTINCT permission.id']);

    if (!RoleScopeCheck.isSuperAdmin(token)) {
      queryBuilder
        .leftJoin('permission.rolePermissions', 'rolePermissions')
        .leftJoin('rolePermissions.role', 'role')
        .leftJoin('role.userRoles', 'userRoles')
        .andWhere('(userRoles.userId = :userId AND permission.deleted = FALSE)', {
          userId: token.id,
        });
    }

    return queryBuilder.getRawMany();
  }

  async validatePermissionIdsByUser(token: TokenPayloadModel, permissionIdsParam: string[]) {
    const queryBuilder = this.createQueryBuilder('permission')
      .where('permission.id IN (:...permissionIdsParam)', { permissionIdsParam })
      .select(['DISTINCT permission.id']);

    if (!RoleScopeCheck.isSuperAdmin(token)) {
      queryBuilder
        .leftJoin('permission.rolePermissions', 'rolePermissions')
        .leftJoin('rolePermissions.role', 'role')
        .leftJoin('role.userRoles', 'userRoles')
        .andWhere('(userRoles.userId = :userId AND permission.deleted = FALSE)', {
          userId: token.id,
        });
    }

    const permissions = await queryBuilder.getRawMany();

    return permissions.length === permissionIdsParam.length;
  }

  async _initSpecificPermission() {
    return await this.connection.transaction(async (managerTrans) => {
      //insert action
      const actionName = ActionEnum.REVIEW;
      await managerTrans.save(Action, { name: actionName });

      // select list feature id by feature name
      const featureNames = [
        `${FeatureEnum.QUALITY_ASSURANCE_SELF_ASSESSMENT}::${SubFeatureEnum.SELF_ASSESSMENT}`,
        // `${FeatureEnum.QUALITY_ASSURANCE_PILOT_TERMINAL_FEEDBACK}::${SubFeatureEnum.PILOT_TERMINAL_FEEDBACK}`,
      ];
      const featureIds = await managerTrans.find(Feature, {
        where: { name: In(featureNames) },
        select: ['id'],
      });

      // select list action id by action name
      const actionNames = [`${ActionEnum.REVIEW}`];
      const actionIds = await managerTrans.find(Action, {
        where: { name: In(actionNames) },
        select: ['id'],
      });
      const listPermission = [];
      for (let i = 0; i < featureIds.length; i++) {
        for (let j = 0; j < actionIds.length; j++) {
          listPermission.push({ featureId: featureIds[i].id, actionId: actionIds[j].id });
        }
      }

      await managerTrans.save(Permission, listPermission);
      await managerTrans.save(IAMMetaConfig, {
        key: META_KEY.ACTION_PERMISSION,
        lastTimeSync: ROLE_CONFIG_DATA.ACTION_PERMISSION_VERSION,
      });
    });
  }
}
