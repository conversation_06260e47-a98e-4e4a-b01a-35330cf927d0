import { Process, Processor } from '@nestjs/bull';
import { Job } from 'bull';
import { Logger<PERSON>om<PERSON>, RoleScope } from 'svm-nest-lib';
import { TokenCacheService } from '../../../infras/redis/token-cache.service';
import { QUEUE_NAME } from '../../../micro-services/async/queue-name.const';

export interface ITokenDeletingModel {
  entity: 'user' | 'company';
  parentCompanyId?: string;
  companyId?: string;
  userId?: string;
  roleScope?: RoleScope;
}

// Consumer subscribes to 'users_change' queue
// Should create consumer in each business module
@Processor(QUEUE_NAME.TOKEN_DELETING)
export class TokenDeletingConsumer {
  private readonly logger = new LoggerCommon(TokenDeletingConsumer.name);

  constructor(private readonly tokenCacheService: TokenCacheService) {}

  @Process()
  async handleDeleteToken(job: Job<ITokenDeletingModel>) {
    try {
      await this.tokenCacheService.deleteTokenKeys(job.data);
      this.logger.log('[handleDeleteToken] done: ' + JSON.stringify(job.data));
    } catch (ex) {
      this.logger.error('[handleDeleteToken] error');
      console.error(ex);
      throw ex;
    }
  }
}
