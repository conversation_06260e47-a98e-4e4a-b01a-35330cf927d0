import { Process, Processor } from '@nestjs/bull';
import { Job } from 'bull';
import { Logger<PERSON>ommon } from 'svm-nest-lib';
import { DEFAULT_ROLES_INSPECTION_QA } from '../../../commons/consts/default-inspection-qa.const';
import { DEFAULT_ROLES_INSPECTION } from '../../../commons/consts/default-inspection.const';
import { DEFAULT_ROLES_QA } from '../../../commons/consts/default-qa.const';
import { CompanyLevelEnum, DataChangeEvent } from '../../../commons/enums';
import { QUEUE_NAME } from '../../../micro-services/async/queue-name.const';
import { WorkflowRepository } from '../../work-flow/repository/workflow.repository';
import { RoleRepository } from '../repository/role.repository';

export interface ICompanyEventModel {
  operation: DataChangeEvent;
  companyData: {
    id: string;
    code: string;
    name: string;
    companyLevel?: string;
    isInspection?: boolean;
    isQA?: boolean;
  };
}

// Consumer subscribes to 'users_change' queue
// Should create consumer in each business module
@Processor(QUEUE_NAME.COMPANIES_CHANGE_SUB1)
export class CompanyCreatingConsumer {
  private readonly logger = new LoggerCommon(CompanyCreatingConsumer.name);

  constructor(
    private readonly roleRepo: RoleRepository,
    private readonly workflowRepository: WorkflowRepository,
  ) {}

  @Process()
  async handleCompanyCreating(job: Job<ICompanyEventModel>) {
    try {
      const isPackageQA = job.data?.companyData?.isQA || null;
      // const companyLevel = job.data?.companyData?.companyLevel || null;
      const isPackageInspection = job.data?.companyData?.isInspection || null;
      let roleDefaultSync = [];
      if (isPackageQA && !isPackageInspection) {
        roleDefaultSync = DEFAULT_ROLES_QA;
      }
      if (!isPackageQA && isPackageInspection) {
        roleDefaultSync = DEFAULT_ROLES_INSPECTION;
      }
      if (isPackageQA && isPackageInspection) {
        roleDefaultSync = DEFAULT_ROLES_INSPECTION_QA;
      }
      // create default role
      // await this.roleRepo._createDefaultRolesHelper(
      //   false,
      //   [job.data.companyData.id],
      //   null,
      //   roleDefaultSync,
      // );

      // create default operator workflow for self assessment
      // if (isPackageQA && companyLevel === CompanyLevelEnum.MAIN_COMPANY) {
      //   await this.workflowRepository.createDefaultWorkflowForSelfAssessment(
      //     job.data.companyData.id,
      //   );
      // }

      this.logger.log('[handleCompanyCreating] done: ' + JSON.stringify(job.data));
    } catch (ex) {
      this.logger.error('[handleCompanyCreating] error', ex.message || ex);
      throw ex;
    }
  }
}
