import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Param,
  ParseU<PERSON><PERSON>ipe,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { IAMService } from './iam.service';
import { ListQueryDto } from '../../commons/dtos';
import {
  AuthGuard,
  RequiredPermissions,
  Roles,
  RoleScope,
  RolesGuard,
  TokenDecorator,
  TokenPayloadModel,
} from 'svm-nest-lib';
import { CreateRoleDto, ListRoleDto, UpdateRoleDto } from './dto';
import { ActionEnum, FeatureEnum, SubFeatureEnum } from '../../commons/enums';
import { CreateDefaultRolesDto } from './dto/create-default-roles.dto';

@ApiTags('Role Permission')
@Controller('iam')
@ApiBearerAuth()
@UseGuards(AuthGuard, RolesGuard)
export class IAMController {
  constructor(private readonly iamService: IAMService) {}

  @ApiResponse({ description: 'Create new role success', status: HttpStatus.CREATED })
  @ApiOperation({ summary: 'Create new role' })
  @ApiBody({ type: CreateRoleDto, description: 'Create role object body' })
  @Roles(RoleScope.SUPER_ADMIN, RoleScope.ADMIN)
  @RequiredPermissions({
    feature: FeatureEnum.USER_ROLE + '::' + SubFeatureEnum.ROLE_AND_PERMISSION,
    action: ActionEnum.CREATE,
  })
  @Post('/role')
  async createRole(@TokenDecorator() token: TokenPayloadModel, @Body() body: CreateRoleDto) {
    return this.iamService.createRole(token, body);
  }

  @ApiResponse({ description: 'Get list roles success', status: HttpStatus.OK })
  @ApiOperation({ summary: 'Get list roles' })
  @Roles(RoleScope.SUPER_ADMIN, RoleScope.ADMIN, RoleScope.USER)
  @Get('/role')
  async listRoles(@TokenDecorator() token: TokenPayloadModel, @Query() query: ListRoleDto) {
    return this.iamService.listRoles(token, query);
  }

  @ApiResponse({
    description: 'List all role permissions success',
    status: HttpStatus.OK,
  })
  @ApiOperation({ summary: 'List all role permissions' })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @Get('/role-permission')
  async listAllRolePermissions(@TokenDecorator() token: TokenPayloadModel) {
    return this.iamService.getAllUserRolePermission(token.id, {
      companyId: token.companyId,
      parentCompanyId: token.parentCompanyId,
      switch: token.other ? true : false,
    });
  }

  @ApiResponse({ description: 'Get list roles success', status: HttpStatus.OK })
  @ApiOperation({ summary: 'Get list roles' })
  @Roles(RoleScope.SUPER_ADMIN, RoleScope.ADMIN, RoleScope.USER)
  @Get('/user-management/role')
  async listRolesForManageUser(
    @TokenDecorator() user: TokenPayloadModel,
    @Query() query: ListQueryDto,
  ) {
    return this.iamService.listRolesForManageUser(user, query);
  }

  @ApiResponse({
    description: 'Get default role success',
    status: HttpStatus.OK,
  })
  @ApiOperation({ summary: 'Get default role' })
  @Get('/role/default')
  async getDefaultRoles(@TokenDecorator() user: TokenPayloadModel) {
    return this.iamService.getDefaultRoles(user);
  }

  @ApiParam({ name: 'id', type: 'string', required: true })
  @ApiResponse({
    description: 'Get detail role success',
    status: HttpStatus.OK,
  })
  @ApiOperation({ summary: 'Get detail role' })
  @Roles(RoleScope.SUPER_ADMIN, RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.USER_ROLE + '::' + SubFeatureEnum.ROLE_AND_PERMISSION,
    action: ActionEnum.VIEW,
  })
  @Get('/role/:id')
  async getDetailRole(
    @TokenDecorator() user: TokenPayloadModel,
    @Param('id', ParseUUIDPipe) roleId: string,
  ) {
    return this.iamService.getDetailRole(user, roleId);
  }

  @ApiParam({ name: 'id', type: 'string', required: true })
  @ApiBody({ type: UpdateRoleDto, description: 'Update role object body' })
  @ApiResponse({
    description: 'Update role success',
    status: HttpStatus.OK,
  })
  @ApiOperation({ summary: 'Update role' })
  @Roles(RoleScope.SUPER_ADMIN, RoleScope.ADMIN)
  @RequiredPermissions({
    feature: FeatureEnum.USER_ROLE + '::' + SubFeatureEnum.ROLE_AND_PERMISSION,
    action: ActionEnum.UPDATE,
  })
  @Put('/role/:id')
  async updateRole(
    @TokenDecorator() token: TokenPayloadModel,
    @Param('id', ParseUUIDPipe) roleId: string,
    @Body() body: UpdateRoleDto,
  ) {
    return this.iamService.updateRole(token, roleId, body);
  }

  @ApiParam({ name: 'id', type: 'string', required: true })
  @ApiResponse({
    description: 'Delete role success',
    status: HttpStatus.OK,
  })
  @ApiOperation({ summary: 'Delete role' })
  @Roles(RoleScope.SUPER_ADMIN, RoleScope.ADMIN)
  @RequiredPermissions({
    feature: FeatureEnum.USER_ROLE + '::' + SubFeatureEnum.ROLE_AND_PERMISSION,
    action: ActionEnum.DELETE,
  })
  @Delete('/role/:id')
  async deleteRole(
    @TokenDecorator() user: TokenPayloadModel,
    @Param('id', ParseUUIDPipe) roleId: string,
  ) {
    return this.iamService.deleteRole(user, roleId);
  }

  @ApiResponse({ description: 'Get list actions success', status: HttpStatus.OK })
  @ApiOperation({ summary: 'Get list actions' })
  @Roles(RoleScope.SUPER_ADMIN, RoleScope.ADMIN)
  @Get('/action')
  async listAllActions() {
    return this.iamService.listAllActions();
  }

  @ApiQuery({
    description: 'Page params',
    type: ListQueryDto,
    required: false,
  })
  @ApiResponse({ description: 'List all features success', status: HttpStatus.OK })
  @ApiOperation({ summary: 'List all features' })
  @Roles(RoleScope.SUPER_ADMIN, RoleScope.ADMIN)
  @Get('/feature')
  async listFeatures(@Query() query: ListQueryDto) {
    return this.iamService.listFeatures(query);
  }

  @ApiQuery({
    description: 'Page params',
    type: ListQueryDto,
    required: false,
  })
  @ApiResponse({
    description: 'List permissions group by each feature success',
    status: HttpStatus.OK,
  })
  @ApiOperation({ summary: 'List permissions group by each feature' })
  @Roles(RoleScope.SUPER_ADMIN, RoleScope.ADMIN)
  @RequiredPermissions({
    feature: FeatureEnum.USER_ROLE + '::' + SubFeatureEnum.ROLE_AND_PERMISSION,
    action: ActionEnum.VIEW,
  })
  @Get('/permission')
  async listPermissions(@Query() query: ListQueryDto, @TokenDecorator() token: TokenPayloadModel) {
    return this.iamService.listPermissions(query, token);
  }

  @ApiResponse({
    description: 'Migrate role default for company',
    status: HttpStatus.CREATED,
  })
  @ApiResponse({
    description: 'Migrate role default for company error',
    status: HttpStatus.BAD_REQUEST,
  })
  @ApiOperation({
    summary: 'Migrate role default for company',
    operationId: 'migrateDataDefaultForCompany',
  })
  @ApiBody({ type: CreateDefaultRolesDto })
  @Roles(RoleScope.SUPER_ADMIN, RoleScope.ADMIN, RoleScope.USER)
  @Post('role-default')
  async migrateDefaultRoles(
    @TokenDecorator() token: TokenPayloadModel,
    @Body() body: CreateDefaultRolesDto,
  ) {
    return this.iamService.migrateDefaultRoles(token, body);
  }
}
