import { Injectable } from '@nestjs/common';
import {
  <PERSON><PERSON>rror,
  BasicAuthLib,
  ForbiddenError,
  LoggerCommon,
  RoleScope,
  TokenPayloadModel,
} from 'svm-nest-lib';
import { ListQueryDto } from '../../commons/dtos';
import { CreateRoleDto } from './dto/create-role.dto';
import { CreateUserRoleDto } from './dto/create-user-role.dto';
import { Feature } from './entities/feature.entity';
import {
  FeatureRepository,
  ActionRepository,
  PermissionRepository,
} from './repository/permission.repository';
import { RoleRepository, UserRoleRepository } from './repository/role.repository';
import { ListRoleDto, ListRolePermissionDto, UpdateRoleDto } from './dto';
import { CheckTokenDto } from './dto/check-token.dto';
import { SaveTokenDto } from './dto/save-token.dto';
import { TokenCacheService } from '../../infras/redis/token-cache.service';
import { WorkflowService } from '../work-flow/workflow.service';
import { UserRolePermissionRepository } from './repository/user-role-permission.repository';
import { SVMAssetsService } from '../../micro-services/sync/svm-assets.service';
import * as ROLE_CONFIG_DATA from '../iam/config/roles.config';
import { ITokenDeletingModel } from './consumer/token-deleting.consumer';
import { CreateDefaultRolesDto } from './dto/create-default-roles.dto';
import { DEFAULT_ROLES_QA } from '../../commons/consts/default-qa.const';
import { DEFAULT_ROLES_INSPECTION } from '../../commons/consts/default-inspection.const';
import { DEFAULT_ROLES_INSPECTION_QA } from '../../commons/consts/default-inspection-qa.const';

@Injectable()
export class IAMService {
  constructor(
    private readonly svmAssetsService: SVMAssetsService,
    private readonly featureRepository: FeatureRepository,
    private readonly actionRepository: ActionRepository,
    private readonly permissionRepository: PermissionRepository,
    private readonly roleRepository: RoleRepository,
    private readonly userRoleRepository: UserRoleRepository,
    private readonly userRolePermissionRepository: UserRolePermissionRepository,
    private readonly tokenCacheService: TokenCacheService,
    private readonly workflowService: WorkflowService,
  ) {
    this._initDefaultRolesForSuperAdmin();
    this._runSync();
  }

  private async _runSync() {
    await this._initFeatures();
    await this._initSpecificPermissions();
    await this._initDefaultPackageRoles();
    await this._initDefaultRoles();
  }

  private async _initDefaultRoles() {
    try {
      const { flag, version } = await this.roleRepository._checkNeedToSyncDefaultRoles();
      if (!flag) return;

      const companyIds = (await this.svmAssetsService.listAllCompanies()).map((c) => c.id);

      //Sync for old default role
      // await this.roleRepository._createDefaultRolesHelper(
      //   true,
      //   companyIds,
      //   version,
      //   ROLE_CONFIG_DATA.DEFAULT_ROLES,
      // );
      //Sync for new default role
      await this.roleRepository._createDefaultRolesHelper(true, companyIds, version);
    } catch (ex) {
      LoggerCommon.error('[IAMService] _init default roles error', ex.message || ex);
    }
  }

  // This will only run once
  private async _initDefaultRolesForSuperAdmin() {
    try {
      const check = await this.roleRepository._checkInitDefaultRoleForSuperAdmin();
      if (!check) return;
      await this.roleRepository._createDefaultRolesHelperForSuperAdmin();
      await this.userRoleRepository._migrateUserDefaultRole();
    } catch (ex) {
      LoggerCommon.error(
        '[IAMService] _init default roles for super admin error',
        ex.message || ex,
      );
    }
  }

  private async _initDefaultPackageRoles() {
    try {
      const { flag, version } = await this.roleRepository._checkNeedToSyncDefaultPackageRoles();
      if (!flag) return;
      await this.roleRepository._createDefaultPackageRolesHelper(true, version);
    } catch (ex) {
      LoggerCommon.error('[IAMService] _init default package roles error', ex.message || ex);
    }
  }

  private async _initFeatures() {
    try {
      const { flag, version } = await this.featureRepository._checkNeedToSyncFeature();
      if (!flag) return;

      await this.actionRepository._initActions();
      await this.featureRepository._initFeaturesPermissions(true, version);
    } catch (ex) {
      LoggerCommon.error('[IAMService] _init feature action error', ex.message || ex);
    }
  }

  async createRole(token: TokenPayloadModel, body: CreateRoleDto) {
    const roleCreated = await this.roleRepository.createRole(token, body);
    // // Publish role_create event
    // this.roleProducer.publishRoleChange({
    //   operation: DataChangeEvent.INSERT,
    //   roleData: { id: roleCreated.id, name: roleCreated.name },
    // });
    return roleCreated;
  }

  async listRoles(token: TokenPayloadModel, query: ListRoleDto) {
    return this.roleRepository.listRoles(token, query);
  }

  async listRolesForManageUser(user: TokenPayloadModel, query: ListQueryDto) {
    return this.roleRepository.listRolesForManageUser(user.companyId, query);
  }

  async getDefaultRoles(user: TokenPayloadModel) {
    return this.roleRepository.getDefaultRoles(user.companyId);
  }

  async getDetailRole(user: TokenPayloadModel, id: string) {
    const role = await this.roleRepository.getDetailRole(id);
    if (!role || (!role.isDefault && role.companyId !== user.companyId)) {
      throw new BaseError({ status: 404, message: 'role.NOT_FOUND' });
    } else {
      return role;
    }
  }

  async updateRole(token: TokenPayloadModel, roleId: string, body: UpdateRoleDto) {
    await this.roleRepository.updateRole(token, roleId, body);

    // Publish user_update event
    // this.roleProducer.publishRoleChange({
    //   operation: DataChangeEvent.UPDATE,
    //   roleData: { id: roleId, name: body.name },
    // });

    return 1;
  }

  async deleteRole(user: TokenPayloadModel, roleId: string) {
    return this.roleRepository.deleteRole(user, roleId);
  }

  async listAllActions() {
    return this.actionRepository.listAllActions();
  }

  async listFeatures(query: ListQueryDto) {
    return this.featureRepository.listFeatures(query);
  }

  async listPermissions(query: ListQueryDto, token: TokenPayloadModel) {
    const dataList = await this.featureRepository.listPermissionsGroupByFeatures(query, token);
    const res = this._handleSubFeatures(dataList.data);
    return {
      data: res,
      page: dataList.page,
      pageSize: dataList.pageSize,
      totalPage: dataList.totalPage,
      totalItem: dataList.totalItem,
    };
  }

  //#region private method
  private _handleSubFeatures(
    features: (Feature & {
      actions?: {
        id: string;
        name: string;
        createdAt: Date;
      }[];
    })[],
  ) {
    // Init map data
    const featuresMap = new Map<
      string,
      Feature & {
        actions?: {
          id: string;
          name: string;
        }[];
      }
    >();
    for (let i = 0; i < features.length; i++) {
      if (!features[i].children) {
        features[i].children = [];
        features[i].actions = [];
      }

      featuresMap.set(features[i].id, features[i]);
    }

    // Handle nested object - subFeatures
    const res: any[] = [];
    let item: Feature & {
      actions?: {
        id: string;
        name: string;
        createdAt: Date;
      }[];
    };

    for (let i = 0; i < features.length; i++) {
      item = features[i];
      // Check if feature is BIG FEATURE (root feature)
      if (!item.parent) {
        res.push(features[i]);
      } else {
        // append to children list of its parent
        featuresMap.get(item.parent.id).children.push(item);
        // push actions to its parent
        const actionIdsSet = new Set(featuresMap.get(item.parent.id).actions.map((act) => act.id));

        for (let x = 0; x < item.permissions.length; x++) {
          if (!actionIdsSet.has(item.permissions[x].action.id)) {
            featuresMap.get(item.parent.id).actions.push(item.permissions[x].action);
          }
        }
      }
    }

    // Check display of features has permission only
    for (let i = features.length - 1; i >= 0; i--) {
      item = features[i];

      if (!item.isLeaf && item.children.length === 0) {
        // Check if feature is BIG FEATURE (root feature)
        if (!item.parent) {
          res.splice(res.indexOf(item), 1);
        } else {
          const children = featuresMap.get(item.parent.id).children;
          children.splice(children.indexOf(item), 1);
        }
      } else {
        // Bubble actions of mid-features to root features
        if (item.parent) {
          const actionIdsSet = new Set(
            featuresMap.get(item.parent.id).actions.map((act) => act.id),
          );

          for (let x = 0; x < item.actions.length; x++) {
            if (!actionIdsSet.has(item.actions[x].id)) {
              featuresMap.get(item.parent.id).actions.push(item.actions[x]);
            }
          }
        }
      }
    }

    // Re-sort its actions array
    for (let i = 0; i < features.length; i++) {
      item = features[i];
      if (!item.parent) {
        item.actions.sort((x, y) => {
          if (x.createdAt > y.createdAt) return 1;
          return -1;
        });
      }
    }

    return res;
  }

  // private async _initFeaturesPermissions() {
  //   await this.actionRepository._initActions();
  //   await this.featureRepository._initFeaturesAndPermission();
  //   LoggerCommon.log('[IAMService] [_initFeaturesPermissions] OK');
  // }

  //#endregion private method

  //#region Internal services
  async createUserRole(userId: string, body: CreateUserRoleDto) {
    await this.userRoleRepository.createUserRole(userId, body);

    return { userId, roleIds: body.roleIds };
  }

  async updateUserRole(userId: string, body: CreateUserRoleDto) {
    const updatedRoles = await this.userRoleRepository.updateUserRole(userId, body);
    const updateUserRole = {
      userId,
      roleIds: body.roleIds,
      isRemovedRoleInspector: updatedRoles.isRemovedRoleInspector,
      updatedRoles: updatedRoles.updatedRoles,
    };
    return updateUserRole;
  }

  async checkRoleUpdateInspector(userId: string, body: CreateUserRoleDto) {
    const isRemovedRoleInspector = await this.userRoleRepository.checkRoleUpdateInspector(
      userId,
      body,
    );

    return { isRemovedRoleInspector };
  }

  async getAllUserRole(userId: string) {
    return this.roleRepository.getAllRoleByUser(userId);
  }

  async getAllUserRolePermission(userId: string, query: ListRolePermissionDto) {
    let workflowPermissions: string[] = [];
    const rolePermissions = await this.userRolePermissionRepository.getAllRolePermissionByUser(
      userId,
      query,
    );
    if (query.workflow) {
      workflowPermissions = await this.workflowService.listWorkflowPermissionByUser(userId, query);
    }
    return {
      rolePermissions: rolePermissions.map((item) => item.permission),
      workflowPermissions,
    };
  }

  async saveTokenStateful(userId: string, body: SaveTokenDto) {
    const decoded = BasicAuthLib.decodeToken(body.token) as TokenPayloadModel;
    if (userId !== decoded.id) {
      throw new BaseError({});
    }
    return this.tokenCacheService.saveTokenToCache(decoded, body.token);
  }

  async deleteTokenStateful(userId: string, body: SaveTokenDto) {
    const decoded = BasicAuthLib.decodeToken(body.token) as TokenPayloadModel;
    if (userId !== decoded.id) {
      throw new BaseError({});
    }
    const param: ITokenDeletingModel = {
      entity: 'user',
      parentCompanyId: decoded.parentCompanyId ? decoded.parentCompanyId : '',
      companyId: decoded.companyId ? decoded.companyId : '',
      userId: decoded.id ? decoded.id : '',
    };
    return this.tokenCacheService.deleteTokenKeys(param);
  }

  async deleteTokenSpecific(userId: string, body: SaveTokenDto) {
    const decoded = BasicAuthLib.decodeToken(body.token) as TokenPayloadModel;
    if (userId !== decoded.id) {
      throw new BaseError({});
    }
    const param: ITokenDeletingModel = {
      entity: 'user',
      parentCompanyId: decoded.parentCompanyId ? decoded.parentCompanyId : '',
      companyId: decoded.companyId ? decoded.companyId : '',
      userId: decoded.id ? decoded.id : '',
    };
    return this.tokenCacheService.deleteTokenSpecific(param, decoded, body.token);
  }

  async checkTokenStateful(userId: string, body: CheckTokenDto) {
    const decoded = BasicAuthLib.decodeToken(body.token) as TokenPayloadModel;
    if (userId !== decoded.id) {
      throw new BaseError({});
    }
    return this.tokenCacheService.checkTokenInCache(decoded, body.token);
  }

  private async _initSpecificPermissions() {
    try {
      const { flag } = await this.roleRepository._checkNeedToSyncPermission();
      if (!flag) return;

      // insert action permission
      await this.permissionRepository._initSpecificPermission();
    } catch (error) {
      throw new Error(error);
    }
    LoggerCommon.log('[IAMService] [_initSpecificPermissions] OK');
  }

  async migrateDefaultRoles(token: TokenPayloadModel, body: CreateDefaultRolesDto) {
    const companyFound = await this.roleRepository.detailCompany(body.companyId);
    if (!companyFound) {
      throw new BaseError({ status: 400, message: 'Company not found' });
    }

    const isPackageQA = companyFound.isQA || null;
    const isPackageInspection = companyFound.isInspection || null;
    let roleDefaultSync = [];
    if (isPackageQA && !isPackageInspection) {
      roleDefaultSync = DEFAULT_ROLES_QA;
    }
    if (!isPackageQA && isPackageInspection) {
      roleDefaultSync = DEFAULT_ROLES_INSPECTION;
    }
    if (isPackageQA && isPackageInspection) {
      roleDefaultSync = DEFAULT_ROLES_INSPECTION_QA;
    }
    // create default role
    await this.roleRepository._createDefaultRolesHelper(
      false,
      [companyFound.id],
      null,
      roleDefaultSync,
    );
    return 'Success';
  }

  async getUserRolePermission(userId: string, query: ListRolePermissionDto) {
    let workflowPermissions: string[] = [];
    const rolePermissions = await this.userRolePermissionRepository.getUserRolePermissionByUser(
      userId,
      query,
    );
    if (query.workflow) {
      workflowPermissions = await this.workflowService.listWorkflowPermissionByUser(userId, query);
    }
    return {
      rolePermissions: rolePermissions.map((item) => item),
      workflowPermissions,
    };
  }
  //#endregion Internal services
}

/**
 * throw new BaseMultiErrors({
      status: 401,
      errors: [
        { fieldName: 'email', message: 'user.EMAIL_INVALID' },
        { fieldName: 'password', message: 'user.PASSWORD_INCORRECT' },
      ],
    });
 */
