import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsBoolean, IsOptional, IsUUID } from 'class-validator';
import { ListQueryDto } from '../../../commons/dtos';

export class ListRoleDto extends ListQueryDto {
  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  @IsUUID('all')
  companyId?: string;

  @ApiProperty({ type: Boolean, required: false })
  @IsOptional()
  @IsBoolean()
  @Transform((value) => {
    return value === 'true' || value === true || value === 1 || value === '1';
  })
  isDefault?: boolean;
}
