import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsBoolean, IsEnum, IsNotEmpty, IsOptional, IsUUID, ValidateIf } from 'class-validator';
import { WorkflowType } from '../../../commons/enums';

export class ListRolePermissionDto {
  @ApiProperty({ enum: WorkflowType, required: false })
  @ValidateIf((o) => !!o.workflow)
  @IsNotEmpty()
  @IsEnum(WorkflowType)
  workflowType?: string;

  @ApiProperty({ type: Boolean, required: false })
  @IsOptional()
  @Type(() => Boolean)
  @IsBoolean()
  workflow?: boolean;

  @ApiProperty({ type: 'string', required: true })
  @IsNotEmpty()
  @IsUUID('all')
  companyId: string;

  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  parentCompanyId?: string;

  @ApiProperty({ type: <PERSON>olean, required: false })
  @IsOptional()
  @Type(() => Boolean)
  @IsBoolean()
  switch?: boolean;
}
