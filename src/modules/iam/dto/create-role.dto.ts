import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import {
  ArrayMinSize,
  ArrayUnique,
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsUUID,
  MaxLength,
} from 'class-validator';
import { PackageEnum, StatusCommon } from '../../../commons/enums';

export class CreateRoleDto {
  @ApiProperty({ type: 'string' })
  @IsNotEmpty({ message: 'common.REQUIRED_FIELD' })
  @Transform((value: string) => value.trim())
  @MaxLength(128)
  name: string;

  @ApiProperty({ type: 'string' })
  @IsOptional()
  @Transform((value: string) => value.trim())
  @MaxLength(128)
  description?: string;

  @ApiProperty({ type: 'string' })
  @ApiProperty({ enum: StatusCommon })
  @IsEnum(StatusCommon)
  status: string;

  @ApiProperty({ type: [String] })
  @IsUUID('all', { each: true })
  @ArrayUnique()
  @ArrayMinSize(1)
  @IsArray()
  @IsNotEmpty()
  permissions: string[];

  @ApiProperty({ type: 'string' })
  @ApiProperty({ enum: PackageEnum })
  @IsEnum(PackageEnum)
  @IsOptional()
  rolePackage?: string;
}
