import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MyRedisModule } from '../../infras';
import { MicroservicesAsyncModule } from '../../micro-services/async';
import { MicroservicesSyncModule } from '../../micro-services/sync';
import { WorkflowModule } from '../work-flow/workflow.module';
import { IAMInternalController } from './iam-internal.controller';

import { IAMController } from './iam.controller';
import { IAMService } from './iam.service';
import {
  FeatureRepository,
  ActionRepository,
  PermissionRepository,
} from './repository/permission.repository';
import {
  RolePermissionRepository,
  RoleRepository,
  UserRoleRepository,
} from './repository/role.repository';
import { UserRolePermissionRepository } from './repository/user-role-permission.repository';
import { CompanyCreatingConsumer, TokenDeletingConsumer } from './consumer';
import { WorkflowRepository } from '../work-flow/repository/workflow.repository';
@Module({
  imports: [
    TypeOrmModule.forFeature([
      FeatureRepository,
      ActionRepository,
      RoleRepository,
      PermissionRepository,
      RolePermissionRepository,
      UserRoleRepository,
      UserRolePermissionRepository,
      WorkflowRepository,
    ]),
    MyRedisModule,
    MicroservicesAsyncModule,
    MicroservicesSyncModule,
    WorkflowModule,
  ],
  controllers: [IAMController, IAMInternalController],
  providers: [IAMService, CompanyCreatingConsumer, TokenDeletingConsumer],
  exports: [],
})
export class IAMModule {}
