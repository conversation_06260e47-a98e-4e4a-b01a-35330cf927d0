import { ActionEnum, FeatureEnum, SubFeatureEnum } from '../../../commons/enums';

export const FIXED_ROLE_NAME = {
  INSPECTOR: 'Inspector',
  AUDITEE: 'Auditee',
  PILOT: 'Pilot',
};

export const ROLE_NAME_DEFAULT = {
  COMMON: {
    COMPANY_LOCAL_ADMIN: 'Company (Local) Admin',
  },
  INSPECTION: {
    BPO_INSPECTION: 'BPO (Inspection)',
    INSPECTION_PLANNER: 'Inspection Planner',
    INSPECTION_MANAGER: 'Inspection Manager',
    INTERNAL_INSPECTOR: 'Inspector (Internal)',
    EXTERNAL_INSPECTOR: 'Inspector (External)',
    INSPECTION_REPORT_AUDIENCE: 'Inspection Report Audience',
    INSPECTED_PARTY_AUDITEE: 'Inspected Party / Auditee',
  },
  QA: {
    BPO_QA: 'BPO (QA)',
    VETTING_SUPERINTENDANT: 'Vetting Superintendant',
    VETTING_MANAGER: 'Vetting Manager',
    COMMERCIAL_TEAM: 'Commercial Team',
    OPERATOR_DOC_HOLDER: 'Operator / DOC Holder',
    PILOT_TERMINAL: 'Pilot / Terminal',
  },
};

export const FIXED_PACKAGE_ROLE_NAME = {
  INAUTIX_INSPECTIONS_PACKAGE_ROLES: 'Inautix Inspections Package Roles',
  INAUTIX_QA_PACKAGE_ROLES: 'Inautix QA Package Roles',
  INAUTIX_INSPECTIONS_AND_QA_PACKAGE_ROLES: 'Inautix Inspections And QA Package Roles',
};

export const VERSION = '2022-04-18T04:21:00.000z';
export const PACKAGE_ROLE_VERSION = '2024-09-26T00:00:00.000z';
export const ACTION_PERMISSION_VERSION = '2022-07-22T06:58:45.062Z';

export const DEFAULT_ROLES = [
  {
    name: FIXED_ROLE_NAME.INSPECTOR,
    description: FIXED_ROLE_NAME.INSPECTOR,
    permissions: [
      // {
      //   feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.REPORT_TEMPLATE,
      //   actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      // },
      // {
      //   feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.AUDIT_CHECKLIST,
      //   actions: [ActionEnum.VIEW, ActionEnum.EXECUTE],
      // },
      // {
      //   feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.INSPECTION_MAPPING,
      //   actions: [ActionEnum.VIEW],
      // },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.PLANNING_AND_REQUEST,
        actions: [ActionEnum.VIEW, ActionEnum.EXECUTE, ActionEnum.EXPORT],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.AUDIT_TIME_TABLE,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.AUDIT_INSPECTION_WORKSPACE,
        actions: [
          ActionEnum.VIEW,
          ActionEnum.CREATE,
          ActionEnum.UPDATE,
          ActionEnum.DELETE,
          ActionEnum.EXPORT,
        ],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.REPORT_OF_FINDING,
        actions: [ActionEnum.VIEW, ActionEnum.UPDATE, ActionEnum.EXPORT],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.INSPECTION_FOLLOW_UP,
        actions: [ActionEnum.VIEW, ActionEnum.EXECUTE],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.INTERNAL_AUDIT_REPORT,
        actions: [ActionEnum.VIEW, ActionEnum.EXECUTE, ActionEnum.EXPORT],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.VESSEL,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.REPORT_TEMPLATE,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.AUDIT_CHECKLIST,
        actions: [ActionEnum.VIEW, ActionEnum.EXECUTE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.INSPECTION_MAPPING,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.USER_ROLE + '::' + SubFeatureEnum.USER,
        actions: [ActionEnum.VIEW, ActionEnum.UPDATE],
      },
      // add new voyage status feature to default inspector roles
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.VOYAGE_STATUS,
        actions: [ActionEnum.VIEW],
      },
    ],
  },
  {
    name: FIXED_ROLE_NAME.AUDITEE,
    description: FIXED_ROLE_NAME.AUDITEE,
    permissions: [
      // {
      //   feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.REPORT_TEMPLATE,
      //   actions: [ActionEnum.VIEW],
      // },
      // {
      //   feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.AUDIT_CHECKLIST,
      //   actions: [ActionEnum.VIEW, ActionEnum.EXECUTE],
      // },
      // {
      //   feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.INSPECTION_MAPPING,
      //   actions: [ActionEnum.VIEW],
      // },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.PLANNING_AND_REQUEST,
        actions: [ActionEnum.VIEW, ActionEnum.EXECUTE, ActionEnum.EXPORT],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.AUDIT_TIME_TABLE,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.AUDIT_INSPECTION_WORKSPACE,
        actions: [
          ActionEnum.VIEW,
          ActionEnum.CREATE,
          ActionEnum.UPDATE,
          ActionEnum.DELETE,
          ActionEnum.EXPORT,
        ],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.REPORT_OF_FINDING,
        actions: [ActionEnum.VIEW, ActionEnum.UPDATE, ActionEnum.EXPORT],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.INSPECTION_FOLLOW_UP,
        actions: [ActionEnum.VIEW, ActionEnum.EXECUTE],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.INTERNAL_AUDIT_REPORT,
        actions: [ActionEnum.VIEW, ActionEnum.EXECUTE, ActionEnum.EXPORT],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.VESSEL,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.REPORT_TEMPLATE,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.AUDIT_CHECKLIST,
        actions: [ActionEnum.VIEW, ActionEnum.EXECUTE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.INSPECTION_MAPPING,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.USER_ROLE + '::' + SubFeatureEnum.USER,
        actions: [ActionEnum.VIEW, ActionEnum.UPDATE],
      },
      // add new voyage status feature to default auditee roles
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.VOYAGE_STATUS,
        actions: [ActionEnum.VIEW],
      },
    ],
  },
];

export const DEFAULT_PACKAGE_ROLE = [
  // Inspection Roles
  {
    name: FIXED_PACKAGE_ROLE_NAME.INAUTIX_INSPECTIONS_PACKAGE_ROLES,
    description: FIXED_PACKAGE_ROLE_NAME.INAUTIX_INSPECTIONS_PACKAGE_ROLES,
    permissions: [
      {
        feature: FeatureEnum.DASHBOARD + '::' + SubFeatureEnum.DASHBOARD,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CUSTOM_DASHBOARD + '::' + SubFeatureEnum.CUSTOM_DASHBOARD,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.DASHBOARD,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.MAP_VIEW,
        actions: [ActionEnum.VIEW],
      },
      // {
      //   feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.REPORT_TEMPLATE,
      //   actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      // },
      // {
      //   feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.AUDIT_CHECKLIST,
      //   actions: [ActionEnum.VIEW, ActionEnum.EXECUTE],
      // },
      // {
      //   feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.INSPECTION_MAPPING,
      //   actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      // },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.PLANNING_AND_REQUEST,
        actions: [ActionEnum.VIEW, ActionEnum.EXPORT, ActionEnum.EXECUTE, ActionEnum.EMAIL],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.AUDIT_TIME_TABLE,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.AUDIT_INSPECTION_WORKSPACE,
        actions: [
          ActionEnum.VIEW,
          ActionEnum.CREATE,
          ActionEnum.UPDATE,
          ActionEnum.DELETE,
          ActionEnum.EXPORT,
        ],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.REPORT_OF_FINDING,
        actions: [ActionEnum.VIEW, ActionEnum.UPDATE, ActionEnum.EXPORT, ActionEnum.EMAIL],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.INSPECTION_FOLLOW_UP,
        actions: [ActionEnum.VIEW, ActionEnum.EMAIL],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.INTERNAL_AUDIT_REPORT,
        actions: [ActionEnum.VIEW, ActionEnum.EXECUTE, ActionEnum.EXPORT, ActionEnum.EMAIL],
      },
      {
        feature: FeatureEnum.GROUP_COMPANY + '::' + SubFeatureEnum.COMPANY_TYPE,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.GROUP_COMPANY + '::' + SubFeatureEnum.COMPANY,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.USER_ROLE + '::' + SubFeatureEnum.ROLE_AND_PERMISSION,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.USER_ROLE + '::' + SubFeatureEnum.USER,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      //#region Inspection and QA master
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.VESSEL_TYPE,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.AUDIT_TYPE,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.LOCATION_MASTER,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.VIQ,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.DEPARTMENT_MASTER,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.PORT_MASTER,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.VESSEL,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.MAIN_CATEGORY,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.CREW_GROUPING,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.DIVISION,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.WORKFLOW_CONFIGURATION,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.DIVISION_MAPPING,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.CARGO_TYPE,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.CARGO,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.POTENTIAL_RISK,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.OBSERVED_RISK,
        actions: [ActionEnum.VIEW],
      },
      //#endregion Inspection and QA master
      //#region Inspection master
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.REPORT_TEMPLATE,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.AUDIT_CHECKLIST,
        actions: [ActionEnum.VIEW, ActionEnum.EXECUTE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.INSPECTION_MAPPING,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.TOPIC,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.SECOND_CATEGORY,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.THIRD_CATEGORY,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.CDI,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.CHARTER_OWNER,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.RANK_MASTER,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature:
          FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.NATURE_OF_FINDINGS_MASTER,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.FLEET,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.INSPECTOR_TIME_OFF,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.FOCUS_REQUEST,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.APP_TYPE_PROPERTY,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.ATTACHMENT_KIT,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.CATEGORY_MAPPING,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.DEVICE_CONTROL,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.DMS,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.MAIL_TEMPLATE,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.MOBILE_CONFIG,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.REPEATED_FINDING,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.ANSWER_VALUE,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      //#endregion Inspection master
      //#region Voyage Status
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.VOYAGE_STATUS,
        actions: [ActionEnum.CREATE, ActionEnum.VIEW, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      //#endregion Voyage Status
      //#region Vessel Schedule
      {
        feature: FeatureEnum.VESSEL_SCHEDULE + '::' + SubFeatureEnum.VESSEL_SCHEDULE,
        actions: [ActionEnum.CREATE, ActionEnum.VIEW, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      //#endregion Vessel Schedule
      //#CVIQ Masters
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.CVIQ_VERSION,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.CVIQ_CHAPTER,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.CVIQ_CONDITIONALITY,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.CVIQ_DETAILS_MAPPING,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      //#endregion CVIQ Masters
      //#Voyage Type
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.VOYAGE_TYPE,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      //#Voyage Type
      //#Risk Matrix
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.RISK_MATRIX,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      //#endregion Risk Matrix
    ],
  },
  // QA Roles
  {
    name: FIXED_PACKAGE_ROLE_NAME.INAUTIX_QA_PACKAGE_ROLES,
    description: FIXED_PACKAGE_ROLE_NAME.INAUTIX_QA_PACKAGE_ROLES,
    permissions: [
      {
        feature: FeatureEnum.DASHBOARD + '::' + SubFeatureEnum.DASHBOARD,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CUSTOM_DASHBOARD + '::' + SubFeatureEnum.CUSTOM_DASHBOARD,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.GROUP_COMPANY + '::' + SubFeatureEnum.COMPANY_TYPE,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.GROUP_COMPANY + '::' + SubFeatureEnum.COMPANY,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.USER_ROLE + '::' + SubFeatureEnum.ROLE_AND_PERMISSION,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.USER_ROLE + '::' + SubFeatureEnum.USER,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      //#region QA section
      {
        feature: FeatureEnum.QUALITY_ASSURANCE + '::' + SubFeatureEnum.DASHBOARD,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.STANDARD_MASTER,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.ELEMENT_MASTER,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature:
          FeatureEnum.QUALITY_ASSURANCE_SELF_ASSESSMENT + '::' + SubFeatureEnum.SELF_ASSESSMENT,
        actions: [
          ActionEnum.VIEW,
          ActionEnum.CREATE,
          ActionEnum.UPDATE,
          ActionEnum.DELETE,
          ActionEnum.REVIEW,
        ],
      },
      {
        feature:
          FeatureEnum.QUALITY_ASSURANCE_SAILING_REPORT +
          '::' +
          SubFeatureEnum.SAILING_GENERAL_REPORT,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.QUALITY_ASSURANCE_INCIDENTS + '::' + SubFeatureEnum.SUMMARY,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.QUALITY_ASSURANCE_INCIDENTS + '::' + SubFeatureEnum.INCIDENTS,
        actions: [
          ActionEnum.VIEW,
          ActionEnum.CREATE,
          ActionEnum.UPDATE,
          ActionEnum.DELETE,
          ActionEnum.RESTRICTED,
        ],
      },
      {
        feature:
          FeatureEnum.QUALITY_ASSURANCE_PILOT_TERMINAL_FEEDBACK +
          '::' +
          SubFeatureEnum.PILOT_TERMINAL_FEEDBACK,
        actions: [
          ActionEnum.VIEW,
          ActionEnum.CREATE,
          ActionEnum.UPDATE,
          ActionEnum.DELETE,
          ActionEnum.RESTRICTED,
        ],
      },
      {
        feature:
          FeatureEnum.QUALITY_ASSURANCE_PILOT_TERMINAL_FEEDBACK +
          '::' +
          SubFeatureEnum.VESSEL_COMPANY_FEEDBACK,
        actions: [
          ActionEnum.VIEW,
          ActionEnum.CREATE,
          ActionEnum.UPDATE,
          ActionEnum.DELETE,
          ActionEnum.RESTRICTED,
        ],
      },
      {
        feature:
          FeatureEnum.QUALITY_ASSURANCE_VESSEL_SCREENING + '::' + SubFeatureEnum.VESSEL_SCREENING,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      //#endregion QA section
      //#region Inspection and QA masters
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.VESSEL_TYPE,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.AUDIT_TYPE,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.LOCATION_MASTER,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.VIQ,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.DEPARTMENT_MASTER,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.PORT_MASTER,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.VESSEL,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.MAIN_CATEGORY,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.CREW_GROUPING,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.DIVISION,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.WORKFLOW_CONFIGURATION,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.DIVISION_MAPPING,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.CARGO_TYPE,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.CARGO,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.POTENTIAL_RISK,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.OBSERVED_RISK,
        actions: [ActionEnum.VIEW],
      },
      //#endregion Inspection and QA masters
      //#region QA masters
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.AUTHORITY_MASTER,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.EVENT_TYPE,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.TECH_ISSUE_NOTE,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.INJURY_BODY,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.INJURY_MASTER,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.TERMINAL,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.PSC_ACTION,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.PSC_DEFICIENCY,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.TRANSFER_TYPE,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.VESSEL_OWNER_BUSINESS,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.INCIDENT_MASTER,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.PLANS_DRAWINGS_MASTER,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.RISK_FACTOR,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      //#endregion QA masters
      //#region Voyage Status
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.VOYAGE_STATUS,
        actions: [ActionEnum.CREATE, ActionEnum.VIEW, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      //#endregion Voyage Status
      //#region Vessel Schedule
      {
        feature: FeatureEnum.VESSEL_SCHEDULE + '::' + SubFeatureEnum.VESSEL_SCHEDULE,
        actions: [ActionEnum.CREATE, ActionEnum.VIEW, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      //#endregion Vessel Schedule
      //#CVIQ Masters
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.CVIQ_VERSION,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.CVIQ_CHAPTER,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.CVIQ_CONDITIONALITY,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.CVIQ_DETAILS_MAPPING,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      //#endregion CVIQ Masters
      //#Categorization Master
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.CATEGORIZATION_MASTER,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      //#Categorization Master
      //#Voyage Type
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.VOYAGE_TYPE,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      //#Voyage Type
      //#Risk Matrix
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.RISK_MATRIX,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      //#endregion Risk Matrix
    ],
  },
  // Inspection and QA Roles
  {
    name: FIXED_PACKAGE_ROLE_NAME.INAUTIX_INSPECTIONS_AND_QA_PACKAGE_ROLES,
    description: FIXED_PACKAGE_ROLE_NAME.INAUTIX_INSPECTIONS_AND_QA_PACKAGE_ROLES,
    permissions: [
      {
        feature: FeatureEnum.DASHBOARD + '::' + SubFeatureEnum.DASHBOARD,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CUSTOM_DASHBOARD + '::' + SubFeatureEnum.CUSTOM_DASHBOARD,
        actions: [ActionEnum.VIEW],
      },
      //#region Inspeciton section
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.DASHBOARD,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.MAP_VIEW,
        actions: [ActionEnum.VIEW],
      },
      // {
      //   feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.REPORT_TEMPLATE,
      //   actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      // },
      // {
      //   feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.AUDIT_CHECKLIST,
      //   actions: [ActionEnum.VIEW, ActionEnum.EXECUTE],
      // },
      // {
      //   feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.INSPECTION_MAPPING,
      //   actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      // },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.PLANNING_AND_REQUEST,
        actions: [ActionEnum.VIEW, ActionEnum.EXPORT, ActionEnum.EXECUTE, ActionEnum.EMAIL],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.AUDIT_TIME_TABLE,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.AUDIT_INSPECTION_WORKSPACE,
        actions: [
          ActionEnum.VIEW,
          ActionEnum.CREATE,
          ActionEnum.UPDATE,
          ActionEnum.DELETE,
          ActionEnum.EXPORT,
        ],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.REPORT_OF_FINDING,
        actions: [ActionEnum.VIEW, ActionEnum.UPDATE, ActionEnum.EXPORT, ActionEnum.EMAIL],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.INSPECTION_FOLLOW_UP,
        actions: [ActionEnum.VIEW, ActionEnum.EMAIL],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.INTERNAL_AUDIT_REPORT,
        actions: [ActionEnum.VIEW, ActionEnum.EXECUTE, ActionEnum.EXPORT, ActionEnum.EMAIL],
      },
      //#endregion Inspection section
      //#region QA section
      {
        feature: FeatureEnum.QUALITY_ASSURANCE + '::' + SubFeatureEnum.DASHBOARD,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.STANDARD_MASTER,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.ELEMENT_MASTER,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature:
          FeatureEnum.QUALITY_ASSURANCE_SELF_ASSESSMENT + '::' + SubFeatureEnum.SELF_ASSESSMENT,
        actions: [
          ActionEnum.VIEW,
          ActionEnum.CREATE,
          ActionEnum.UPDATE,
          ActionEnum.DELETE,
          ActionEnum.REVIEW,
        ],
      },
      {
        feature:
          FeatureEnum.QUALITY_ASSURANCE_SAILING_REPORT +
          '::' +
          SubFeatureEnum.SAILING_GENERAL_REPORT,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.QUALITY_ASSURANCE_INCIDENTS + '::' + SubFeatureEnum.SUMMARY,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.QUALITY_ASSURANCE_INCIDENTS + '::' + SubFeatureEnum.INCIDENTS,
        actions: [
          ActionEnum.VIEW,
          ActionEnum.CREATE,
          ActionEnum.UPDATE,
          ActionEnum.DELETE,
          ActionEnum.RESTRICTED,
        ],
      },
      {
        feature:
          FeatureEnum.QUALITY_ASSURANCE_PILOT_TERMINAL_FEEDBACK +
          '::' +
          SubFeatureEnum.PILOT_TERMINAL_FEEDBACK,
        actions: [
          ActionEnum.VIEW,
          ActionEnum.CREATE,
          ActionEnum.UPDATE,
          ActionEnum.DELETE,
          ActionEnum.RESTRICTED,
        ],
      },
      {
        feature:
          FeatureEnum.QUALITY_ASSURANCE_PILOT_TERMINAL_FEEDBACK +
          '::' +
          SubFeatureEnum.VESSEL_COMPANY_FEEDBACK,
        actions: [
          ActionEnum.VIEW,
          ActionEnum.CREATE,
          ActionEnum.UPDATE,
          ActionEnum.DELETE,
          ActionEnum.RESTRICTED,
        ],
      },
      {
        feature:
          FeatureEnum.QUALITY_ASSURANCE_VESSEL_SCREENING + '::' + SubFeatureEnum.VESSEL_SCREENING,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      //#endregion QA section
      //#region Common section
      {
        feature: FeatureEnum.GROUP_COMPANY + '::' + SubFeatureEnum.COMPANY_TYPE,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.GROUP_COMPANY + '::' + SubFeatureEnum.COMPANY,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.USER_ROLE + '::' + SubFeatureEnum.ROLE_AND_PERMISSION,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.USER_ROLE + '::' + SubFeatureEnum.USER,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      //#endregion Common section
      //#region Inspection and QA master
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.VESSEL_TYPE,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.AUDIT_TYPE,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.LOCATION_MASTER,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.VIQ,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.DEPARTMENT_MASTER,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.PORT_MASTER,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.VESSEL,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.MAIN_CATEGORY,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.CREW_GROUPING,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.DIVISION,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.WORKFLOW_CONFIGURATION,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.DIVISION_MAPPING,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.CARGO_TYPE,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.CARGO,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.POTENTIAL_RISK,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.OBSERVED_RISK,
        actions: [ActionEnum.VIEW],
      },
      //#endregion Inspection and QA master
      //#region Inspection master
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.REPORT_TEMPLATE,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.AUDIT_CHECKLIST,
        actions: [ActionEnum.VIEW, ActionEnum.EXECUTE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.INSPECTION_MAPPING,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.TOPIC,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.SECOND_CATEGORY,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.THIRD_CATEGORY,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.CDI,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.CHARTER_OWNER,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.RANK_MASTER,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature:
          FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.NATURE_OF_FINDINGS_MASTER,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.FLEET,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.INSPECTOR_TIME_OFF,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.FOCUS_REQUEST,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.APP_TYPE_PROPERTY,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.ATTACHMENT_KIT,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.CATEGORY_MAPPING,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.DEVICE_CONTROL,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.DMS,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.MAIL_TEMPLATE,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.MOBILE_CONFIG,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.REPEATED_FINDING,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.ANSWER_VALUE,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      //#endregion Inspection master
      //#region QA masters
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.AUTHORITY_MASTER,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.EVENT_TYPE,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.TECH_ISSUE_NOTE,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.INJURY_BODY,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.INJURY_MASTER,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.TERMINAL,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.PSC_ACTION,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.PSC_DEFICIENCY,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.TRANSFER_TYPE,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.VESSEL_OWNER_BUSINESS,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.INCIDENT_MASTER,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.PLANS_DRAWINGS_MASTER,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.RISK_FACTOR,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      //#endregion QA masters
      //#region Voyage Status
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.VOYAGE_STATUS,
        actions: [ActionEnum.CREATE, ActionEnum.VIEW, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      //#endregion Voyage Status
      //#region Vessel Schedule
      {
        feature: FeatureEnum.VESSEL_SCHEDULE + '::' + SubFeatureEnum.VESSEL_SCHEDULE,
        actions: [ActionEnum.CREATE, ActionEnum.VIEW, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      //#endregion Vessel Schedule
      //#CVIQ Masters
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.CVIQ_VERSION,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.CVIQ_CHAPTER,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.CVIQ_CONDITIONALITY,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.CVIQ_DETAILS_MAPPING,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      //#endregion CVIQ Masters
      //#Categorization Master
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.CATEGORIZATION_MASTER,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      //#Categorization Master
      //#Voyage Type
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.VOYAGE_TYPE,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      //#Voyage Type
      //#Risk Matrix
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.RISK_MATRIX,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      //#endregion Risk Matrix
    ],
  },
];
