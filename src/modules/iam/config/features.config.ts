import { ACTIONS_CONFIG } from '.';

export interface IFeature {
  name: string;
  order: number;
  isLeaf: boolean;
  description?: string;
  subFeatures?: IFeature[];
  actions?: string[];
}

export const FEATURE_SYNC_VERSION = '2024-09-26T00:00:00.000z';

export const FEATURES_CONFIG: IFeature[] = [
  //#region Dashboard
  {
    name: 'Dashboard',
    description: 'Dashboard',
    order: 5,
    isLeaf: false,
    subFeatures: [
      {
        name: 'Dashboard',
        description: 'Dashboard',
        order: 6,
        isLeaf: true,
        actions: [ACTIONS_CONFIG.View],
      },
    ],
  },
  //#endregion
  //#region Custom Dashboard
  {
    name: 'Custom Dashboard',
    description: 'Custom Dashboard',
    order: 7,
    isLeaf: false,
    subFeatures: [
      {
        name: 'Custom Dashboard',
        description: 'Custom Dashboard',
        order: 8,
        isLeaf: true,
        actions: [ACTIONS_CONFIG.View],
      },
    ],
  },
  //#endregion
  //#region Audit and Inspection
  {
    name: 'Audit & Inspection',
    description: 'Audit & Inspection',
    order: 10,
    isLeaf: false,
    subFeatures: [
      {
        name: 'Dashboard',
        description: 'Inspection Dashboard',
        order: 13,
        isLeaf: true,
        actions: [ACTIONS_CONFIG.View],
      },
      {
        name: 'Map view',
        description: 'Map View',
        order: 15,
        isLeaf: true,
        actions: [ACTIONS_CONFIG.View],
      },
      // {
      //   name: 'Report template master',
      //   order: 20,
      //   isLeaf: true,
      //   actions: [
      //     ACTIONS_CONFIG.View,
      //     ACTIONS_CONFIG.Create,
      //     ACTIONS_CONFIG.Update,
      //     ACTIONS_CONFIG.Delete,
      //   ],
      // },
      // {
      //   name: 'Audit Checklist',
      //   order: 30,
      //   isLeaf: true,
      //   actions: [ACTIONS_CONFIG.View, ACTIONS_CONFIG.Execute],
      // },
      // {
      //   name: 'Inspection Mapping',
      //   order: 40,
      //   isLeaf: true,
      //   actions: [
      //     ACTIONS_CONFIG.View,
      //     ACTIONS_CONFIG.Create,
      //     ACTIONS_CONFIG.Update,
      //     ACTIONS_CONFIG.Delete,
      //   ],
      // },
      {
        name: 'Planning & request',
        description: 'Planning',
        order: 50,
        isLeaf: true,
        actions: [
          ACTIONS_CONFIG.View,
          ACTIONS_CONFIG.Execute,
          ACTIONS_CONFIG.Export,
          ACTIONS_CONFIG.Email,
        ],
      },
      {
        name: 'Audit Time Table',
        description: 'Inspection Time Table',
        order: 55,
        isLeaf: true,
        actions: [
          ACTIONS_CONFIG.View,
          ACTIONS_CONFIG.Create,
          ACTIONS_CONFIG.Update,
          ACTIONS_CONFIG.Delete,
        ],
      },
      {
        name: 'Audit Inspection Workspace',
        description: 'Inspection Workspace',
        order: 60,
        isLeaf: true,
        actions: [
          ACTIONS_CONFIG.View,
          ACTIONS_CONFIG.Create,
          ACTIONS_CONFIG.Update,
          ACTIONS_CONFIG.Delete,
          ACTIONS_CONFIG.Export,
        ],
      },
      {
        name: 'Report of Findings',
        description: 'Report of Findings',
        order: 65,
        isLeaf: true,
        actions: [
          ACTIONS_CONFIG.View,
          ACTIONS_CONFIG.Execute,
          ACTIONS_CONFIG.Export,
          ACTIONS_CONFIG.Email,
        ],
      },
      {
        name: 'Internal Audit Report',
        description: 'Inspection Report',
        order: 68,
        isLeaf: true,
        actions: [
          ACTIONS_CONFIG.View,
          ACTIONS_CONFIG.Execute,
          ACTIONS_CONFIG.Export,
          ACTIONS_CONFIG.Email,
        ],
      },
      {
        name: 'Inspection Follow Up',
        description: 'Inspection Follow Up',
        order: 70,
        isLeaf: true,
        actions: [ACTIONS_CONFIG.View, ACTIONS_CONFIG.Email],
      },
    ],
  },
  //#endregion
  //#region Group and Company
  {
    name: 'Group & Company',
    description: 'Group & Company',
    order: 90,
    isLeaf: false,
    subFeatures: [
      {
        name: 'Company Type',
        description: 'Company Type',
        order: 95,
        isLeaf: true,
        actions: [ACTIONS_CONFIG.View],
      },
      {
        name: 'Group master',
        description: 'Group Master',
        order: 100,
        isLeaf: true,
      },
      {
        name: 'Company',
        description: 'Company',
        order: 110,
        isLeaf: true,
        actions: [
          ACTIONS_CONFIG.View,
          ACTIONS_CONFIG.Create,
          ACTIONS_CONFIG.Update,
          ACTIONS_CONFIG.Delete,
        ],
      },
    ],
  },
  //#endregion
  //#region User and Roles
  {
    name: 'User & Roles',
    description: 'User & Roles',
    order: 120,
    isLeaf: false,
    subFeatures: [
      {
        name: 'Role and permission',
        description: 'Role and Permission',
        order: 130,
        isLeaf: true,
        actions: [
          ACTIONS_CONFIG.View,
          ACTIONS_CONFIG.Create,
          ACTIONS_CONFIG.Update,
          ACTIONS_CONFIG.Delete,
        ],
      },
      {
        name: 'User',
        description: 'User',
        order: 140,
        isLeaf: true,
        actions: [
          ACTIONS_CONFIG.View,
          ACTIONS_CONFIG.Create,
          ACTIONS_CONFIG.Update,
          ACTIONS_CONFIG.Delete,
        ],
      },
    ],
  },
  //#endregion
  //#region Quality Assurance
  {
    name: 'Quality Assurance',
    description: 'Quality Assurance',
    order: 160,
    isLeaf: false,
    subFeatures: [
      {
        name: 'Dashboard',
        description: 'Dashboard',
        order: 165,
        isLeaf: true,
        actions: [ACTIONS_CONFIG.View],
      },
      {
        name: 'Self-Assessment',
        description: 'Self-Assessment',
        order: 170,
        isLeaf: false,
        subFeatures: [
          {
            name: 'Self-Assessment',
            description: 'Self-Assessment',
            order: 200,
            isLeaf: true,
            actions: [ACTIONS_CONFIG.View, ACTIONS_CONFIG.Execute],
          },
        ],
      },
      {
        name: 'Sailing Report',
        description: 'Sailing Report',
        order: 250,
        isLeaf: false,
        subFeatures: [
          {
            name: 'Sailing General Report',
            description: 'Sailing General Report',
            order: 260,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
        ],
      },
      {
        name: 'Incidents',
        description: 'Incidents',
        order: 270,
        isLeaf: false,
        subFeatures: [
          {
            name: 'Summary',
            description: 'Summary',
            order: 280,
            isLeaf: true,
            actions: [ACTIONS_CONFIG.View],
          },
          {
            name: 'Incidents',
            description: 'Incidents',
            order: 290,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
              ACTIONS_CONFIG.Restricted,
            ],
          },
        ],
      },
      {
        name: 'Pilot/Terminal Feedback',
        description: 'Feedback',
        order: 292,
        isLeaf: false,
        subFeatures: [
          {
            name: 'Pilot/Terminal Feedback',
            description: 'Pilot Feedback',
            order: 294,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
              ACTIONS_CONFIG.Restricted,
            ],
          },
          {
            name: 'Vessel/Company Feedback',
            description: 'Vessel/Company Feedback',
            order: 295,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
              ACTIONS_CONFIG.Restricted,
            ],
          },
        ],
      },
      {
        name: 'Vessel Screening',
        description: 'Vessel Screening',
        order: 300,
        isLeaf: false,
        subFeatures: [
          {
            name: 'Vessel Screening',
            description: 'Vessel Screening',
            order: 320,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
        ],
      },
    ],
  },
  //#endregion
  {
    name: 'Vessel Schedule',
    description: 'Vessel Schedule',
    order: 350,
    isLeaf: false,
    subFeatures: [
      {
        name: 'Vessel Schedule',
        description: 'Vessel Schedule',
        order: 360,
        isLeaf: true,
        actions: [
          ACTIONS_CONFIG.View,
          ACTIONS_CONFIG.Create,
          ACTIONS_CONFIG.Update,
          ACTIONS_CONFIG.Delete,
        ],
      },
    ],
  },
  //#region Configuration
  {
    name: 'Configuration',
    description: 'Configuration',
    order: 600,
    isLeaf: false,
    subFeatures: [
      {
        name: 'Common',
        description: 'Common',
        order: 700,
        isLeaf: false,
        subFeatures: [
          // {
          //   name: 'AG-Grid',
          //   description: 'AG-Grid',
          //   order: 705,
          //   isLeaf: true,
          //   actions: [
          //     ACTIONS_CONFIG.View,
          //     ACTIONS_CONFIG.Create,
          //     ACTIONS_CONFIG.Update,
          //     ACTIONS_CONFIG.Delete,
          //   ],
          // },
          {
            name: 'Crew Grouping',
            description: 'Crew Grouping',
            order: 710,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
          {
            name: 'Division',
            description: 'Division',
            order: 720,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
          {
            name: 'Department',
            description: 'Department',
            order: 730,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
          {
            name: 'Division Mapping',
            description: 'Division Mapping',
            order: 740,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
          {
            name: 'Audit type',
            description: 'Inspection Type',
            order: 750,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
          {
            name: 'Location master',
            description: 'Location',
            order: 760,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
          {
            name: 'Main category',
            description: 'Main Category',
            order: 770,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
          {
            name: 'Port master',
            description: 'Port',
            order: 780,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
          {
            name: 'Vessel',
            description: 'Vessel',
            order: 790,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
          {
            name: 'Vessel inspection questionnaire',
            description: 'Vessel Inspection Questionnaire',
            order: 800,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
          {
            name: 'Vessel type',
            description: 'Vessel Type',
            order: 810,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
          {
            name: 'Workflow configuration',
            description: 'Workflow Configuration',
            order: 820,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
          {
            name: 'Voyage Status',
            description: 'Voyage Status',
            order: 830,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
          {
            name: 'Cargo',
            description: 'Cargo',
            order: 840,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
          {
            name: 'Cargo Type',
            description: 'Cargo Type',
            order: 850,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
          {
            name: 'CVIQ Version',
            description: 'CVIQ Version',
            order: 860,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
          {
            name: 'CVIQ Chapter',
            description: 'CVIQ Chapter',
            order: 870,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
          {
            name: 'CVIQ Conditionality',
            description: 'CVIQ Conditionality',
            order: 880,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
          {
            name: 'CVIQ Details Mapping',
            description: 'CVIQ Details Mapping',
            order: 890,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
          {
            name: 'Potential Risk',
            description: 'Potential Risk',
            order: 891,
            isLeaf: true,
            actions: [ACTIONS_CONFIG.View],
          },
          {
            name: 'Observed Risk',
            description: 'Observed Risk',
            order: 892,
            isLeaf: true,
            actions: [ACTIONS_CONFIG.View],
          },
          {
            name: 'Voyage Type',
            description: 'Voyage Type',
            order: 893,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
          {
            name: 'Risk Matrix',
            description: 'Risk Matrix',
            order: 894,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
          {
            name: 'Cause Analysis Master',
            description: 'Cause Analysis Master',
            order: 410,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
        ],
      },
      {
        name: 'Inspection',
        description: 'Inspection',
        order: 900,
        isLeaf: false,
        subFeatures: [
          {
            name: 'App Type Property',
            description: 'App Type Property',
            order: 910,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
          {
            name: 'Attachment Kit',
            description: 'Attachment Kit',
            order: 920,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
          {
            name: 'Category mapping',
            description: 'Category Mapping',
            order: 930,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
          {
            name: 'Charter/Owner',
            description: 'Charter/Owner',
            order: 940,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
          {
            name: 'Chemical Distribution Institute',
            description: 'Chemical Distribution Institute',
            order: 950,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
          {
            name: 'Device Control',
            description: 'Device Control Manager',
            order: 960,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
          {
            name: 'DMS',
            description: 'DMS',
            order: 970,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
          {
            name: 'Fleet',
            description: 'Fleet',
            order: 980,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
          {
            name: 'Focus request',
            description: 'Focus Request',
            order: 990,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
          {
            name: 'Audit Checklist',
            description: 'Inspection Checklist Template',
            order: 991,
            isLeaf: true,
            actions: [ACTIONS_CONFIG.View, ACTIONS_CONFIG.Execute],
          },
          {
            name: 'Inspection Mapping',
            description: 'Inspection Mapping',
            order: 992,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
          {
            name: 'Mail Template',
            description: 'Mail Management',
            order: 1000,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
          {
            name: 'Mobile config',
            description: 'Mobile Config',
            order: 1010,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
          {
            name: 'Nature of Findings',
            description: 'Nature of Findings',
            order: 1020,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
          {
            name: 'Rank',
            description: 'Rank',
            order: 1030,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
          {
            name: 'Repeated Finding',
            description: 'Repeated Finding Calculation',
            order: 1035,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
          {
            name: 'Report template master',
            description: 'Report Template Master',
            order: 1036,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
          {
            name: 'Second category',
            description: 'Second Category',
            order: 1040,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
          {
            name: 'Third category',
            description: 'Third Category',
            order: 1040,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
          {
            name: 'Inspector time off',
            description: 'Time Off Management',
            order: 1060,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
          {
            name: 'Topic',
            description: 'Topic',
            order: 1070,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
          {
            name: 'Answer Value',
            description: 'Value Management',
            order: 1090,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
        ],
      },
      {
        name: 'QA',
        description: 'QA',
        order: 1100,
        isLeaf: false,
        subFeatures: [
          {
            name: 'Authority master',
            description: 'Authority Master',
            order: 1110,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
          {
            name: 'Element Master',
            description: 'Element Master',
            order: 1135,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
          {
            name: 'Event Type',
            description: 'Event Type',
            order: 1140,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
          {
            name: 'Incident Master',
            description: 'Incident Type',
            order: 1150,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
          {
            name: 'Injury Body',
            description: 'Body Parts Injury',
            order: 1160,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
          {
            name: 'Injury Master',
            description: 'Injury Master',
            order: 1170,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
          {
            name: 'Technical Issue Note',
            description: 'Issue Note',
            order: 1180,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
          {
            name: 'Plans Drawings Master',
            description: 'Plans and Drawings',
            order: 1190,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
          {
            name: 'PSC Action',
            description: 'PSC Action',
            order: 1200,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
          {
            name: 'PSC Deficiency',
            description: 'PSC Deficiency',
            order: 1210,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
          {
            name: 'Risk factor',
            description: 'Risk Factor',
            order: 1220,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
          {
            name: 'Standard Master',
            description: 'Standard Master',
            order: 1225,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
          {
            name: 'Terminal',
            description: 'Terminal',
            order: 1230,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
          {
            name: 'Transfer Type',
            description: 'Transfer Type',
            order: 1240,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
          {
            name: 'Vessel Owner Business',
            description: 'Vessel Owner Business',
            order: 1250,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
          {
            name: 'Categorization Master',
            description: 'Categorization Master',
            order: 1260,
            isLeaf: true,
            actions: [
              ACTIONS_CONFIG.View,
              ACTIONS_CONFIG.Create,
              ACTIONS_CONFIG.Update,
              ACTIONS_CONFIG.Delete,
            ],
          },
        ],
      },
    ],
  },
  //#endregion
];
