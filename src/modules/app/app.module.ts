import { Module } from '@nestjs/common';
import { APP_FILTER } from '@nestjs/core';

import { AppController } from './app.controller';
import { AppService } from './app.service';
import { HttpExceptionFilter } from 'svm-nest-lib';
import { DatabaseModule, MessageBrokerModule, MyRedisModule } from '../../infras';
import { I18nConfigModule } from '../../configs/i18n';
import { HealthModule } from '../commons/health/health.module';
import { IAMModule } from '../iam/iam.module';
import { WorkflowModule } from '../work-flow/workflow.module';
import { IAMMetaConfigModule } from '../commons/iam-meta-config/iam-meta-config.module';

@Module({
  imports: [
    DatabaseModule, // persistent DBs
    MyRedisModule, // redis cache
    MessageBrokerModule,
    I18nConfigModule,
    HealthModule,
    IAMMetaConfigModule,
    IAMModule,
    WorkflowModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    {
      provide: APP_FILTER,
      useClass: HttpExceptionFilter,
    },
  ],
})
export class AppModule {}
