import { Entity, Column, <PERSON>ToOne, PrimaryGeneratedColumn } from 'typeorm';
import { IdentifyEntity } from 'svm-nest-lib';
import { WorkflowPermission } from './../../../commons/enums';
import { Role } from './../../iam/entities/role.entity';
import { Workflow } from './work-flow.entity';

@Entity()
export class WorkflowRole extends IdentifyEntity {
  @PrimaryGeneratedColumn('uuid')
  public id: string;

  @Column({ type: 'uuid' })
  public workflowId: string;

  @Column({ type: 'uuid' })
  public roleId: string;

  @Column({ type: 'enum', enum: WorkflowPermission })
  public permission: string;

  @Column({ type: 'uuid', nullable: true })
  public createdUserId: string;

  @Column({ type: 'uuid', nullable: true })
  public updatedUserId?: string;

  @ManyToOne(() => Role, (role) => role.workflowRoles, { onDelete: 'CASCADE' })
  role: Role;

  @ManyToOne(() => Workflow, (workflow) => workflow.workflowRoles, { onDelete: 'CASCADE' })
  workflow: Workflow;
}
