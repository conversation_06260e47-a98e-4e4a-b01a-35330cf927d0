import { Entity, Column, Index, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { IdentifyEntity } from 'svm-nest-lib';
import { DBIndexes } from './../../../commons/consts/db.const';
import { WorkflowStatus, ApproverType, WorkflowType } from './../../../commons/enums';
import { WorkflowRole } from './work-flow-role.entity';

@Entity()
@Index(
  DBIndexes.IDX_WORK_FLOW_VERSION_COMPANY_ID_WORK_FLOW_TYPE,
  ['version', 'companyId', 'workflowType'],
  {
    unique: true,
    where: 'deleted = false',
  },
)
export class Workflow extends IdentifyEntity {
  @PrimaryGeneratedColumn('uuid')
  public id: string;

  @Column({ type: 'uuid' })
  public companyId: string;

  @Column({ type: 'enum', enum: WorkflowType })
  public workflowType: string;

  @Column({ type: 'enum', enum: ApproverType, default: ApproverType.WITHOUT_BUDGET_AMOUNT })
  public approverType: string;

  @Column({ type: 'enum', enum: WorkflowStatus })
  public status: string;

  @Column({ type: 'uuid', nullable: true })
  public createdUserId: string;

  @Column({ type: 'uuid', nullable: true })
  public updatedUserId?: string;

  @Column({ nullable: true })
  public description: string;

  @Column()
  public version: string;

  @OneToMany(() => WorkflowRole, (workflowRole) => workflowRole.workflow)
  workflowRoles: WorkflowRole[];
}
