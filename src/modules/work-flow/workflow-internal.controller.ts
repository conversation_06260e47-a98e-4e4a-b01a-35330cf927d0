import {
  Body,
  Controller,
  Get,
  HttpStatus,
  Param,
  ParseUUIDPipe,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AuthInternalGuard } from 'svm-nest-lib';

import { WorkflowService } from './workflow.service';

import { ListRolePermissionDto } from '../iam/dto';
import { ListStepWorkflowTypePermissionDto } from './dto';
import { ListUserAuditorsInCompaniesBodyDto } from './dto/list-user-auditors-in-companies-body.dto';
import { ListPRGraphicallyDTO } from './dto/list-pr-group-graphical.dto';

@ApiTags('Internal Workflow configuration')
@Controller('internal/workflow')
@ApiBearerAuth()
@UseGuards(AuthInternalGuard)
export class InternalWorkflowController {
  constructor(private readonly workflowService: WorkflowService) {}

  @ApiResponse({
    description: 'List users are auditor in companies',
    status: HttpStatus.BAD_REQUEST,
  })
  @ApiOperation({ summary: 'List users are auditor in companies' })
  @ApiBody({ type: ListUserAuditorsInCompaniesBodyDto })
  @Post('/planning-request/auditors-in-companies')
  async listUserAuditorsInCompanies(@Body() body: ListUserAuditorsInCompaniesBodyDto) {
    return await this.workflowService.listUserAuditorsInCompaniesInternal(
      body.companyParentId,
      body.childCompanyIds,
    );
  }

  @ApiResponse({
    description: 'list active workflow permissions for current user success',
    status: HttpStatus.BAD_REQUEST,
  })
  @ApiOperation({ summary: 'Get list active permissions internal' })
  @Get('/active-user-permission/:userId')
  async listActiveUserPermissions(
    @Param('userId', ParseUUIDPipe) userId: string,
    @Query() query: ListRolePermissionDto,
  ) {
    return await this.workflowService.listWorkflowPermissionByUser(userId, {
      workflowType: query.workflowType,
      companyId: query.companyId,
    } as ListRolePermissionDto);
  }

  @ApiResponse({
    description: 'List all auditors',
    status: HttpStatus.BAD_REQUEST,
  })
  @ApiOperation({ summary: 'Get list auditors by company' })
  @Get('/auditors/:companyId')
  async listAuditors(
    @Param('companyId', ParseUUIDPipe) companyId: string,
    @Query() query: ListPRGraphicallyDTO,
  ) {
    return await this.workflowService.listAuditors(companyId, query);
  }

  @ApiResponse({
    description: 'List step workflow type and workflow permission success',
    status: HttpStatus.BAD_REQUEST,
  })
  @ApiOperation({ summary: 'List work flow' })
  @Get('/workflow-type-permission-step/:companyId')
  async listStepWorkflowTypePermissionInternal(
    @Param('companyId', ParseUUIDPipe) companyId: string,
    @Query() query: ListStepWorkflowTypePermissionDto,
  ) {
    return await this.workflowService.listStepWorkflowTypePermissionInternal(companyId, {
      workflowType: query.workflowType,
    });
  }
}
