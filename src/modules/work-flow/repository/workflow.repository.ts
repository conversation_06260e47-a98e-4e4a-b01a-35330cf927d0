import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Token<PERSON>ayloadModel, TypeORMRepository, Utils } from 'svm-nest-lib';
import { EntityRepository, EntityManager, Connection, In } from 'typeorm';
import { omit } from 'lodash';
import { Workflow } from '../entities/work-flow.entity';
import { WorkflowRole } from '../entities/work-flow-role.entity';
import { CreateWorkflowDto } from '../dto/create-workflow.dto';
import {
  CompanyLevelEnum,
  WorkflowPermission,
  WorkflowStatus,
  WorkflowType,
} from '../../../commons/enums';
import {
  ListWorkflowDto,
  UpdateWorkflowDto,
  ListUserWorkflowTypePermissionDto,
  ListStepWorkflowTypePermissionDto,
} from '../dto';
import { DBIndexes } from '../../../commons/consts/db.const';
import { Role } from '../../iam/entities/role.entity';
import { ROLE_NAME_DEFAULT } from '../../iam/config/roles.config';
import { RoleRepository } from '../../iam/repository/role.repository';
import { ListQueryDto } from 'src/commons/dtos';

@EntityRepository(Workflow)
export class WorkflowRepository extends TypeORMRepository<Workflow> {
  constructor(private readonly connection: Connection) {
    super();
  }

  async createWorkflow(user: TokenPayloadModel, body: CreateWorkflowDto) {
    try {
      const prepareWorkflow = {
        ...omit(body, ['workflowRoles']),
        companyId: user.companyId,
        createdUserId: user.id,
        status: WorkflowStatus.PUBLISHED,
      };

      const rs = await this.connection.transaction(async (manager) => {
        // Inactivate old work flow
        await this._inactiveOldWorkflow(body.workflowType, user.companyId);
        const version = await this._getNextVersionWorkflow(body.workflowType, user.companyId);
        const data = {
          ...prepareWorkflow,
          version: version.toString(),
        };
        const newRecord = await manager.save(Workflow, data);

        const prepareWorkflowRoles = body.workflowRoles.map((item) => {
          return {
            ...item,
            workflowId: newRecord.id,
            companyId: user.companyId,
          };
        });
        // Insert workflow roles
        await manager.insert(WorkflowRole, prepareWorkflowRoles);
        return newRecord;
      });
      return rs;
    } catch (ex) {
      LoggerCommon.error('[WorkflowRepository] createWorklow error ', ex.message || ex);
      if (ex.constraint === DBIndexes.IDX_WORK_FLOW_VERSION_COMPANY_ID_WORK_FLOW_TYPE) {
        throw new BaseError({ status: 400, message: 'workflow.WORKFLOW_EXISTED' });
      }
      throw ex;
    }
  }

  async listWorkflow(user: TokenPayloadModel, query: ListWorkflowDto, companyIds: string[]) {
    const queryBuilder = this.createQueryBuilder('workflow').select();

    if (query.approverType) {
      queryBuilder.andWhere('workflow.approverType = :approverType', {
        approverType: query.approverType,
      });
    }

    if (query.workflowType) {
      queryBuilder.andWhere('workflow.workflowType = :workflowType', {
        workflowType: query.workflowType,
      });
    }

    if (query.status) {
      queryBuilder.andWhere('workflow.status = :status', {
        status: query.status,
      });
    }

    // get array condition where
    queryBuilder.andWhere('(workflow.companyId IN (:...companyIds))', {
      companyIds,
    });

    if (query.companyId) {
      queryBuilder.andWhere('(workflow.companyId = :companyId)', {
        companyId: query.companyId,
      });
    }

    const dataList = await this.list(
      {
        page: Number(query.page) || undefined,
        limit: Number(query.pageSize) || undefined,
      },
      {
        queryBuilder,
        sort: query.sort || 'workflow.createdAt:-1',
      },
    );

    return dataList;
  }

  async getDetailWorkflow(
    companyId: string,
    workflowId: string,
    companyIds: string[],
    query: ListQueryDto,
  ) {
    const queryBuilder = await this.createQueryBuilder('workflow')
      .leftJoinAndSelect('workflow.workflowRoles', 'workflowRoles')
      .select()
      .where('workflow.id = :workflowId', {
        companyId,
        workflowId,
      });

    // get active users only in workflow configurations
    if (query.status) {
      queryBuilder.leftJoinAndSelect('workflowRoles.role', 'role', 'role.status = :status', {
        status: query.status,
      });
    } else {
      queryBuilder.leftJoinAndSelect('workflowRoles.role', 'role');
    }

    // get array condition where
    queryBuilder.andWhere('(workflow.companyId IN (:...companyIds))', {
      companyIds,
    });

    const workflow = await this.getOneQB(queryBuilder);

    if (workflow) {
      return workflow;
    } else {
      throw new BaseError({ status: 404, message: 'workflow.NOT_FOUND' });
    }
  }

  async deleteWorkflow(companyId: string, workflowId: string) {
    // soft delete
    const updateResult = await this.softDelete({
      id: workflowId,
      companyId,
    });
    if (updateResult.affected === 0) {
      throw new BaseError({ status: 404, message: 'workflow.NOT_FOUND' });
    } else {
      return 1;
    }
  }

  async updateWorkflow(user: TokenPayloadModel, workflowId: string, body: UpdateWorkflowDto) {
    try {
      // prepared list workflow-roles record to insert
      const preparedWorkflowRoles = body.workflowRoles.map((workflowRole) => {
        return {
          workflowId,
          roleId: workflowRole.roleId,
          permission: workflowRole.permission,
          id: Utils.strings.generateUUID(),
        };
      });

      const prepareWorkFlow = {
        ...omit(body, ['workflowRoles']),
        updatedUserId: user.id,
      };

      const rs = await this.connection.transaction(async (manager) => {
        await manager.update(Workflow, workflowId, prepareWorkFlow);
        await manager.delete(WorkflowRole, {
          workflowId,
        });
        return await manager.insert(WorkflowRole, preparedWorkflowRoles);
      });
      return rs;
    } catch (ex) {
      LoggerCommon.error('[WorkflowRepository] createWorkflow error ', ex.message || ex);
      if (ex.constraint === DBIndexes.IDX_WORK_FLOW_VERSION_COMPANY_ID_WORK_FLOW_TYPE) {
        throw new BaseError({ status: 400, message: 'workflow.WORKFLOW_EXISTED' });
      }
      throw ex;
    }
  }

  async _inactiveOldWorkflow(workflowType: string, companyId: string) {
    return await this.update(
      { workflowType, companyId, deleted: false },
      {
        status: WorkflowStatus.INACTIVE,
      },
    );
  }

  async _getNextVersionWorkflow(workflowType: string, companyId: string) {
    const workflow = await this.findOne({
      where: {
        workflowType,
        companyId,
      },
      select: ['id', 'version'],
      order: { createdAt: -1 },
    });

    if (!workflow) {
      return 1;
    }
    return Number(workflow.version) + 1;
  }

  async _getCurrentWFRoles(workflowId: string, companyId: string) {
    const workflowRoles = await this.getOneQB(
      this.createQueryBuilder('workflow')
        .leftJoinAndSelect('workflow.workflowRoles', 'workflowRoles')
        .select()
        .where('workflow.companyId = :companyId AND workflow.id = :workflowId', {
          companyId,
          workflowId,
        }),
    );

    return workflowRoles;
  }

  async listUserWorkflowTypePermission(
    token: TokenPayloadModel,
    query: ListUserWorkflowTypePermissionDto,
  ) {
    let queryRaw = `
      select distinct 
        wf."workflowType" as "workflowType",
        wfr."permission" as "wfrPermission",
        ur."userId" as "userId",
        r."name" as "roleName"
    from workflow wf 
    left join workflow_role wfr on wf.id = wfr."workflowId"
    left join "role" r on wfr."roleId" = r."id"
    left join "user_role" ur on r."id" = ur."roleId"
    left join "user" u on ur."userId" = u.id
    left join "company" c on u."companyId" = c.id
    where wf.deleted = false and wf."companyId" = '${token.companyId}' AND wf.status = 'Published'
     AND (u."companyId" = '${token.companyId}' OR u."parentCompanyId" = '${token.companyId}')
     `;

    if (query.workflowType) {
      queryRaw += ` And wf."workflowType" = '${query.workflowType}'`;
    }
    if (query.workflowPermission) {
      queryRaw += ` And wfr."permission" = '${query.workflowPermission}'`;
    }

    // if (token.companyLevel === CompanyLevelEnum.EXTERNAL_COMPANY) {
    //   queryRaw += ` And u."companyId" = '${token.explicitCompanyId}'`;
    // }

    // if (token.companyLevel === CompanyLevelEnum.INTERNAL_COMPANY) {
    //   queryRaw += ` And (u."companyId" = '${token.explicitCompanyId}' OR ( u."parentCompanyId" = '${token.companyId}' AND c."companyLevel" = '${CompanyLevelEnum.EXTERNAL_COMPANY}') ) `;
    // }

    queryRaw += ' GROUP BY wfr."permission", wf.id, wfr.id, r.id, ur.id order by wfr."permission"';
    // console.log(queryRaw);
    const listUserWorkflow = await this.connection.query(queryRaw);
    // check existed user role
    const listUserId = [];
    for (const userWorkflow of listUserWorkflow) {
      if (userWorkflow.userId && userWorkflow.roleName) {
        const queryRaw = `SELECT r.id FROM role r left join user_role ur 
                          on r.id = ur."roleId" where ur."userId" ='${userWorkflow.userId}' and r.name ='${userWorkflow.roleName}'`;
        const checkUser = await this.connection.query(queryRaw);
        // console.log('checkUser', queryRaw);
        // console.log('checkUser', checkUser);
        if (checkUser && checkUser.length > 0) {
          if (listUserId.indexOf(userWorkflow.userId) === -1) {
            listUserId.push(userWorkflow.userId);
          }
        }
      }
    }

    return listUserId;
  }

  async listUserByWorkflowType(token: TokenPayloadModel, query: ListUserWorkflowTypePermissionDto) {
    let queryRaw = `
      select distinct 
        wf."workflowType" as "workflowType",
        wfr."permission" as "wfrPermission",
        ur."userId" as "userId",
        r."name" as "roleName"
    from workflow wf 
    left join workflow_role wfr on wf.id = wfr."workflowId"
    left join "role" r on wfr."roleId" = r."id"
    left join "user_role" ur on r."id" = ur."roleId"
    left join "user" u on ur."userId" = u.id
    left join "company" c on u."companyId" = c.id
    where wf.deleted = false and wf."companyId" = '${token.companyId}' AND wf.status = 'Published'
     AND (u."companyId" = '${token.companyId}' OR u."parentCompanyId" = '${token.companyId}')`;

    if (query.workflowType) {
      queryRaw += ` And wf."workflowType" = '${query.workflowType}'`;
    }

    queryRaw += ' GROUP BY wfr."permission", wf.id, wfr.id, r.id, ur.id order by wfr."permission"';
    const listUserWorkflow = await this.connection.query(queryRaw);
    // check existed user role
    const listUserId = [];
    const mapUserPermission = new Map<string, string[]>();
    for (const userWorkflow of listUserWorkflow) {
      if (!userWorkflow.userId || !userWorkflow.roleName) {
        continue;
      }
      const queryRaw = `SELECT r.id FROM role r left join user_role ur 
                          on r.id = ur."roleId" where ur."userId" ='${userWorkflow.userId}' and r.name ='${userWorkflow.roleName}'`;
      const checkUser = await this.connection.query(queryRaw);
      if (!checkUser || !checkUser.length) {
        continue;
      }
      if (!listUserId.includes(userWorkflow.userId)) {
        listUserId.push(userWorkflow.userId);
      }
      mapUserPermission.has(userWorkflow.wfrPermission)
        ? mapUserPermission.get(userWorkflow.wfrPermission).push(userWorkflow.userId)
        : mapUserPermission.set(userWorkflow.wfrPermission, [userWorkflow.userId]);
    }
    return { listUserId, mapUserPermission: mapUserPermission };
  }

  async listStepWorkflowTypePermission(
    token: TokenPayloadModel,
    query: ListStepWorkflowTypePermissionDto,
  ) {
    let queryRaw = `
      select distinct
        wf."workflowType" as "workflowType",
        wfr."permission" as "wfrPermission",
        CASE wfr."permission"
          WHEN 'creator' THEN 1
          WHEN 'reviewer1' THEN 2
          WHEN 'reviewer2' THEN 3
          WHEN 'reviewer3' THEN 4
          WHEN 'reviewer4' THEN 5
          WHEN 'reviewer5' THEN 6
          WHEN 'approver' THEN 7
          WHEN 'auditor' THEN 8
          WHEN 'close_out' THEN 9
          WHEN 'owner/manager' THEN 10
          WHEN 'publisher' THEN 11
          WHEN 'verification' THEN 12
        ELSE 13
      END AS "permissionOrder"
    from workflow wf 
    left join workflow_role wfr on wf.id = wfr."workflowId"
    left join "role" r on wfr."roleId" = r."id"
    left join "user_role" ur on r."id" = ur."roleId"
    where wf."companyId" = '${token.companyId}' AND wf.status = 'Published' AND wf.deleted = false
     `;
    if (query.workflowType) {
      queryRaw += ` And wf."workflowType" = '${query.workflowType}'`;
    }
    if (query.workflowPermission) {
      queryRaw += ` And wfr."permission" = '${query.workflowPermission}'`;
    }
    queryRaw += ` GROUP BY wfr."permission", wf.id, wfr.id, r.id order by "permissionOrder"`;
    console.log(queryRaw);
    return this.connection.query(queryRaw);
  }

  async createDefaultWorkflowForSelfAssessment(companyId: string) {
    try {
      const role = await this.manager.getCustomRepository(RoleRepository).findOne({
        name: ROLE_NAME_DEFAULT.QA.OPERATOR_DOC_HOLDER,
        companyId,
      });

      if (role) {
        const prepareWorkflow = {
          workflowType: WorkflowType.SELF_ASSESSMENT,
          companyId,
          status: WorkflowStatus.PUBLISHED,
          version: '1',
        };

        const rs = await this.connection.transaction(async (manager) => {
          const newRecord = await manager.save(Workflow, prepareWorkflow);
          const workflowPermissions = [
            { permission: WorkflowPermission.CREATOR },
            { permission: WorkflowPermission.REVIEWER },
            { permission: WorkflowPermission.PUBLISHER },
          ];
          const prepareWorkflowRoles = workflowPermissions.map((item) => {
            return {
              ...item,
              roleId: role.id,
              workflowId: newRecord.id,
              companyId,
            };
          });
          await manager.insert(WorkflowRole, prepareWorkflowRoles);
          return newRecord;
        });
        return rs;
      }
    } catch (ex) {
      LoggerCommon.error(
        '[WorkflowRepository] createDefaultWorkflowForSelfAssessment error ',
        ex.message || ex,
      );
      if (ex.constraint === DBIndexes.IDX_WORK_FLOW_VERSION_COMPANY_ID_WORK_FLOW_TYPE) {
        throw new BaseError({ status: 400, message: 'workflow.WORKFLOW_EXISTED' });
      }
      throw ex;
    }
  }

  async listStepWorkflowTypePermissionInternal(
    companyId: string,
    query: ListStepWorkflowTypePermissionDto,
  ) {
    let queryRaw = `
      select distinct 
        wf."workflowType" as "workflowType",
        wfr."permission" as "wfrPermission",
        CASE wfr."permission"
          WHEN 'creator' THEN 1
          WHEN 'reviewer1' THEN 2
          WHEN 'reviewer2' THEN 3
          WHEN 'reviewer3' THEN 4
          WHEN 'reviewer4' THEN 5
          WHEN 'reviewer5' THEN 6
          WHEN 'approver' THEN 7
          WHEN 'auditor' THEN 8
          WHEN 'close_out' THEN 9
          WHEN 'owner/manager' THEN 10
          WHEN 'publisher' THEN 11
          WHEN 'verification' THEN 12
        ELSE 13
    END AS "permissionOrder"
    from workflow wf 
    left join workflow_role wfr on wf.id = wfr."workflowId"
    left join "role" r on wfr."roleId" = r."id"
    left join "user_role" ur on r."id" = ur."roleId"
    where wf."companyId" = '${companyId}' AND wf.status = 'Published' AND wf.deleted = false`;
    if (query.workflowType) {
      queryRaw += ` And wf."workflowType" = '${query.workflowType}'`;
    }
    if (query.workflowPermission) {
      queryRaw += ` And wfr."permission" = '${query.workflowPermission}'`;
    }
    queryRaw += ' GROUP BY wfr."permission", wf.id, wfr.id, r.id order by "permissionOrder"';
    console.log(queryRaw);
    return this.connection.query(queryRaw);
  }

  async getWorkflowByRecordId(query: ListUserWorkflowTypePermissionDto) {
    let recordCondition;
    const moduleName = query.workflowType;
    switch (moduleName) {
      case WorkflowType.PLANNING_REQUEST:
        recordCondition = `ua."planningRequestId"`;
        break;
      case WorkflowType.AUDIT_CHECKLIST:
        recordCondition = `ua."auditChecklistId"`;
        break;
      case WorkflowType.REPORT_FINDING:
        recordCondition = `ua."reportFindingFormId"`;
        break;
      case WorkflowType.INTERNAL_AUDIT_REPORT:
        recordCondition = `ua."internalAuditReportId"`;
        break;
      case WorkflowType.SELF_ASSESSMENT:
        recordCondition = `ua."selfAssessmentId"`;
        break;
      case WorkflowType.INCIDENTS:
        recordCondition = `ua."incidentInvestigationId"`;
        break;
    }

    let queryRaw = `
    select ua."permission" as permission, ua."userId" as userId
    from user_assignment ua 
    where ${recordCondition} = '${query.recordId}'
    and ua.deleted = false `;
    const listWorkflow = await this.connection.query(queryRaw);
    const userIds = [];
    const approver = [];
    const mapPermission = new Map<string, string[]>();
    for (const item of listWorkflow) {
      if (!userIds.includes(item.userid) && item.userid) {
        userIds.push(item.userid);
      }
      if (!mapPermission.has(item.permission)) {
        mapPermission.set(item.permission, [item.userid]);
      } else {
        mapPermission.get(item.permission).push(item.userid);
      }
      if (WorkflowPermission.APPROVER === item.permission) {
        approver.push(item);
      }
    }
    const checkApprover = approver.length > 0;
    return {
      mapPermission,
      userIds,
      checkApprover,
    };
  }

  async getVesselData(vesselId?: string) {
    const query = `SELECT v.id as "vesselId", v.name as "vesselName", d.id as "divisionId", d.code as "divisionCode",
    d.name as "divisionName", c.name as "docHolderName", c.id as "docHolderId"
    FROM vessel v
    LEFT JOIN vessel_doc_holder vdc ON vdc."vesselId" = v.id
    LEFT JOIN division_mapping dm ON dm."vesselId" = v.id
    LEFT JOIN division d ON d.id = dm."divisionId"
    LEFT JOIN company c ON c.id = CASE WHEN vdc.status = 'active' AND vdc."toDate" is NULL THEN vdc."companyId" END
    WHERE v.id = '${vesselId}'`;
    return this.connection.query(query);
  }
}
