import { Injectable } from '@nestjs/common';
import { differenceWith, uniqBy } from 'lodash';
import { ListQueryDto } from 'src/commons/dtos';
import { UserModel } from 'src/micro-services/sync/model/user.model';
import { BaseError, TokenPayloadModel } from 'svm-nest-lib';
import {
  CompanyLevelEnum,
  CompanyTypeEnum,
  EntityTypePlanningRequest,
  WORKFLOW_TYPE_MAP_PERMISSIONS,
  WorkflowPermission,
  WorkflowType,
} from '../../commons/enums';
import { SVMAssetsService } from '../../micro-services/sync/svm-assets.service';
import { SVMUserService } from '../../micro-services/sync/svm-user.service';
import { ListRolePermissionDto } from '../iam/dto';
import { RoleRepository, UserRoleRepository } from '../iam/repository/role.repository';
import {
  CreateWorkflowDto,
  ListStepWorkflowTypePermissionDto,
  ListUserAuditorsBodyDto,
  ListUserWorkflowTypePermissionDto,
  ListWorkflowDto,
  UpdateWorkflowDto,
} from './dto';
import { ListUserAuditorsInCompaniesBodyDto } from './dto/list-user-auditors-in-companies-body.dto';
import { WorkflowRepository } from './repository/workflow.repository';
import { ListPRGraphicallyDTO } from './dto/list-pr-group-graphical.dto';
import { UserRole } from '../iam/entities/user-role.entity';

@Injectable()
export class WorkflowService {
  constructor(
    private readonly workflowRepository: WorkflowRepository,
    private readonly roleRepository: RoleRepository,
    private readonly userRoleRepository: UserRoleRepository,
    private readonly svmUserService: SVMUserService,
    private readonly svmAssetsService: SVMAssetsService,
  ) {}

  async createWorkflow(user: TokenPayloadModel, body: CreateWorkflowDto) {
    this._validateWorkflowPermissions(body);
    return this.workflowRepository.createWorkflow(user, body);
  }

  async listWorkflow(user: TokenPayloadModel, query: ListWorkflowDto) {
    const detailCompany = await this.svmAssetsService.getDetailCompany(user.companyId);
    const listAllChildCompanies = await this.svmAssetsService.listAllChildCompanies(user.companyId);

    const arrCompany = [
      { id: detailCompany.id, name: detailCompany.name },
      ...listAllChildCompanies,
    ];

    const companyIds = arrCompany.map((item) => item.id);

    const dataList = await this.workflowRepository.listWorkflow(user, query, companyIds);

    // Loop dataList
    for (let i = 0; i < dataList.data.length; i++) {
      for (let j = 0; j < arrCompany.length; j++) {
        if (dataList.data[i].companyId === arrCompany[j].id) {
          dataList.data[i]['company'] = {
            id: arrCompany[j].id,
            name: arrCompany[j].name,
          };
        }
      }
    }
    return dataList;
  }

  async getDetailWorkflow(user: TokenPayloadModel, workflowId: string, query: ListQueryDto) {
    const detailCompany = await this.svmAssetsService.getDetailCompany(user.companyId);
    const listAllChildCompanies = await this.svmAssetsService.listAllChildCompanies(user.companyId);

    const arrCompany = [
      { id: detailCompany.id, name: detailCompany.name },
      ...listAllChildCompanies,
    ];
    const companyIds = arrCompany.map((item) => item.id);

    return this.workflowRepository.getDetailWorkflow(user.companyId, workflowId, companyIds, query);
  }

  async deleteWorkflow(user: TokenPayloadModel, workflowId: string) {
    return this.workflowRepository.deleteWorkflow(user.companyId, workflowId);
  }

  async updateWorkflow(user: TokenPayloadModel, workflowId: string, body: UpdateWorkflowDto) {
    this._validateWorkflowPermissions(body);
    return this.workflowRepository.updateWorkflow(user, workflowId, body);
  }

  async listWorkflowPermissionByUser(userId: string, query: ListRolePermissionDto) {
    const rolesWorkflows = await this.roleRepository.listWorkflowPermissionByUser(userId, query);
    const workflowPermissionSet = new Set<string>();

    // Transform data
    for (let i = 0; i < rolesWorkflows.length; i++) {
      for (let j = 0; j < rolesWorkflows[i].workflowRoles.length; j++) {
        workflowPermissionSet.add(rolesWorkflows[i].workflowRoles[j].permission);
      }
    }

    return [...workflowPermissionSet];
  }

  async listUserWorkflowTypePermission(
    token: TokenPayloadModel,
    query: ListUserWorkflowTypePermissionDto,
  ) {
    const listUserIds = await this.workflowRepository.listUserWorkflowTypePermission(token, query);
    if (listUserIds.length > 0) {
      const userRole = await this.userRoleRepository.listUserRoleByUserIds(listUserIds);

      const listUsers: any[] = await this.svmUserService.getUsernamesByIds(
        listUserIds,
        query.vesselId,
        query.vesselId ? token.explicitCompanyId : undefined,
        token.companyLevel,
      );

      for (const user of listUsers) {
        user.role = [];
        for (const role of userRole) {
          if (user.id === role.userId) {
            user.role.push(role.role.name);
          }
        }
      }

      if (query.workflowType === WorkflowType.INCIDENTS) {
        const listUsersOfNonExternal = listUsers.filter(
          (x) => x.company.companyLevel !== CompanyLevelEnum.EXTERNAL_COMPANY,
        );
        return listUsersOfNonExternal;
      }

      return listUsers;
    }
    return [];
  }
  async listStepWorkflowTypePermission(
    token: TokenPayloadModel,
    query: ListStepWorkflowTypePermissionDto,
  ) {
    return this.workflowRepository.listStepWorkflowTypePermission(token, query);
  }

  async listUserByWorkflowType(token: TokenPayloadModel, query: ListUserWorkflowTypePermissionDto) {
    const userByPermission = await this.workflowRepository.listUserByWorkflowType(token, query);
    const listIds = new Set(userByPermission.listUserId);

    let recordWfl: any;

    const mapUserPermission = userByPermission.mapUserPermission;
    let permissions = Array.from(mapUserPermission.keys());
    const response = [];
    if (query.recordId) {
      recordWfl = await this.workflowRepository.getWorkflowByRecordId(query);
      for (const key of recordWfl.mapPermission.keys()) {
        if (!permissions.includes(key)) {
          permissions.push(key);
        }
      }

      if (recordWfl.checkApprover) {
        permissions = Array.from(recordWfl.mapPermission.keys());
      }
      for (const item of recordWfl.userIds) {
        listIds.add(item);
      }
    }
    const listUserIds = [...listIds];
    // Get infor for User
    const userRole = await this.userRoleRepository.listUserRoleByUserIds(listUserIds);
    let listUsers: any[] = await this.svmUserService.getUsernamesByIds(
      listUserIds,
      query.vesselId,
      query.vesselId ? token.explicitCompanyId : undefined,
      token.companyLevel,
    );
    this.getInforDetailForUser(listUsers, userRole);
    // User assignment will be fetched based on workflow, business division, doc holder, and the external company itself
    if (query.vesselId) {
      const users = [];
      const vesselData = await this.workflowRepository.getVesselData(query.vesselId);

      // Main Company
      if (token.companyLevel === CompanyLevelEnum.MAIN_COMPANY) {
        users.push(
          ...this.getMainAndInternalAndExternalUsers(
            listUsers,
            vesselData,
            token.companyLevel,
            CompanyLevelEnum.INTERNAL_COMPANY,
          ),
        );
        users.push(...this.getNonDOCExternalUsers(listUsers));
        users.push(...this.getDOCExternalUsers(listUsers, vesselData));
      }
      // Internal Company
      if (token.companyLevel === CompanyLevelEnum.INTERNAL_COMPANY) {
        users.push(
          ...this.getMainAndInternalAndExternalUsers(
            listUsers,
            vesselData,
            token.companyLevel,
            CompanyLevelEnum.MAIN_COMPANY,
          ),
        );
        users.push(...this.getNonDOCExternalUsers(listUsers));
        users.push(...this.getDOCExternalUsers(listUsers, vesselData));
      }
      // External Company
      if (token.companyLevel === CompanyLevelEnum.EXTERNAL_COMPANY) {
        users.push(
          ...this.getMainAndInternalAndExternalUsers(
            listUsers,
            vesselData,
            CompanyLevelEnum.MAIN_COMPANY,
            CompanyLevelEnum.INTERNAL_COMPANY,
          ),
        );
        users.push(...this.getDOCExternalUsers(listUsers, vesselData));
        if (token.explicitCompanyId !== vesselData[0].docHolderId) {
          users.push(...listUsers.filter((item) => item.company.id === token.explicitCompanyId));
        }
      }
      listUsers = users;
    }

    if (query.workflowType === WorkflowType.INCIDENTS) {
      const listUsersOfNonExternal = listUsers.filter(
        (x) => x.company.companyLevel !== CompanyLevelEnum.EXTERNAL_COMPANY,
      );
      return listUsersOfNonExternal;
    }

    for (const item of permissions) {
      response.push({
        wfrPermission: item,
        users: mapUserPermission.has(item)
          ? this.listUserByUserIds(mapUserPermission.get(item), listUsers)
          : this.listUserByUserIds(recordWfl.mapPermission.get(item), listUsers),
      });
    }

    return response;
  }
  getMainAndInternalAndExternalUsers(
    listUsers: any[],
    vesselData: any,
    condition1: any,
    condition2: any,
  ): any {
    return listUsers.filter((item) => {
      if (item.company.companyLevel === condition1 || item.company.companyLevel === condition2) {
        return item.divisions.find((x) => x.code === vesselData[0].divisionCode);
      }
    });
  }
  getNonDOCExternalUsers(listUsers: any[]): any {
    return listUsers.filter(
      (item) =>
        item.company.companyLevel === CompanyLevelEnum.EXTERNAL_COMPANY &&
        item.company.companyTypes.find((x) => x.companyType !== CompanyTypeEnum.SHIP_MAGEMENT),
    );
  }
  getDOCExternalUsers(listUsers: any[], vesselData: any): any {
    return listUsers.filter((item) => {
      return (
        item.company.companyLevel === CompanyLevelEnum.EXTERNAL_COMPANY &&
        item.company.id === vesselData[0].docHolderId
      );
    });
  }
  async listUserAuditorsPR(
    user: TokenPayloadModel,
    body: ListUserAuditorsBodyDto,
    // query: ListUserAuditorsQueryDto,
  ) {
    const data = await this.userRoleRepository.listUserAuditorsPRAddFilter(user, body);
    if (data?.length === 0) return [];

    const listUserByIds = await this.svmUserService.getUsersByIds(
      body.inspectorId ? Array.of(body.inspectorId) : data.map((item) => item.userId),
      body.vesselId,
      body.vesselId ||
        (body.entityType === EntityTypePlanningRequest.OFFICE &&
          user.companyLevel !== CompanyLevelEnum.MAIN_COMPANY)
        ? user.explicitCompanyId
        : body.companyId,
      user.companyLevel,
    );

    // get all inspector or by inspection type
    if (body.inspectionTypeIds) {
      const listValidUser: UserModel[] = [];
      for (let i = 0; i < listUserByIds.length; i++) {
        const providedInspectionsByUser = listUserByIds[i].providedInspections;
        if (providedInspectionsByUser && providedInspectionsByUser.length > 0) {
          for (let j = 0; j < providedInspectionsByUser.length; j++) {
            if (
              body.inspectionTypeIds.includes(providedInspectionsByUser[j].inspectionTypeId) &&
              providedInspectionsByUser[j].isVerified.toString() == 'true' &&
              providedInspectionsByUser[j].serviceProvided.toString() == 'true'
            ) {
              listValidUser.push(listUserByIds[i]);
            }
          }
        }
      }
      return uniqBy(listValidUser, 'id');
    } else {
      return listUserByIds;
    }
  }

  async listUserAuditorsInCompanies(
    user: TokenPayloadModel,
    body: ListUserAuditorsInCompaniesBodyDto,
  ) {
    if (body.childCompanyIds?.length > 0) {
      const childCompanyIdsDB = await this.svmAssetsService.listAllChildCompanies(user.companyId);

      const invalidCompanies = differenceWith(
        body.childCompanyIds,
        [...childCompanyIdsDB, { id: user.companyId }],
        (idDto, { id }) => {
          return idDto == id;
        },
      );

      if (invalidCompanies.length > 0) {
        throw new BaseError({
          status: 404,
          message: 'common.INVALID_LIST_COMPANIES',
        });
      }
    }

    const data = await this.userRoleRepository.listUserAuditorsInCompanies(user.companyId);
    if (data.length === 0) return [];
    let users = await this.svmUserService.getUsersByIds(
      data.map((item) => item.userId),
      undefined,
      undefined,
      undefined,
      user.companyLevel === CompanyLevelEnum.INTERNAL_COMPANY
        ? [user?.companyId]
        : body.childCompanyIds,
    );

    if (body.content) {
      users = users.filter((user) => {
        return user.username.toLowerCase().includes(body.content.toLowerCase());
      });
    }

    return users.filter((user) => {
      // filter by verified status
      const isVerifiedUser = user.providedInspections.some(
        (providedInspection) => providedInspection.isVerified && providedInspection.serviceProvided,
      );

      return isVerifiedUser;
    });
  }

  async listUserAuditorsInCompaniesInternal(companyParentId: string, companyIds: string[]) {
    const data = await this.userRoleRepository.listUserAuditorsInCompanies(companyParentId);
    if (data.length === 0) return [];

    const users = await this.svmUserService.getUsersByIds(
      data.map((item) => item.userId),
      undefined,
      undefined,
      undefined,
      companyIds,
    );

    return users.filter((user) => {
      // filter by verified status
      const isVerifiedUser = user.providedInspections.some(
        (providedInspection) => providedInspection.isVerified && providedInspection.serviceProvided,
      );

      return isVerifiedUser;
    });
  }

  async listAuditors(comapnyId: string, query: ListPRGraphicallyDTO) {
    return this.userRoleRepository.listAuditors(comapnyId, query);
  }

  _validateWorkflowPermissions(body: CreateWorkflowDto | UpdateWorkflowDto) {
    const validPermissions = WORKFLOW_TYPE_MAP_PERMISSIONS[body.workflowType as WorkflowType] || [];
    for (let i = 0; i < body.workflowRoles.length; i++) {
      if (!validPermissions.includes(body.workflowRoles[i].permission as WorkflowPermission)) {
        throw new BaseError({ status: 400, message: 'workflow.PERMISSION_INVALID' });
      }
    }
  }

  async listStepWorkflowTypePermissionInternal(
    companyId: string,
    query: ListStepWorkflowTypePermissionDto,
  ) {
    return this.workflowRepository.listStepWorkflowTypePermissionInternal(companyId, query);
  }

  listUserByUserIds(userIds: string[], users: any[]) {
    const response = [];
    for (const user of users) {
      if (userIds?.includes(user.id)) {
        response.push(user);
      }
    }
    return response;
  }
  getInforDetailForUser(listUsers: any[], userRole: UserRole[]) {
    for (const user of listUsers) {
      user.role = [];
      for (const role of userRole) {
        if (user.id === role.userId) {
          user.role.push(role.role.name);
        }
      }
    }
  }
}
