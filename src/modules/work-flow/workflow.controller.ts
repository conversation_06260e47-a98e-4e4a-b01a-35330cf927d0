import {
  Param,
  Post,
  Get,
  Put,
  Delete,
  Body,
  Controller,
  Query,
  HttpStatus,
  UseGuards,
  ParseUUIDPipe,
} from '@nestjs/common';
import { I18n, I18nContext } from 'nestjs-i18n';
import {
  Roles,
  RoleScope,
  AuthGuard,
  RolesGuard,
  TokenDecorator,
  TokenPayloadModel,
  RequiredPermissions,
} from 'svm-nest-lib';
import {
  ApiParam,
  ApiBody,
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';

import { WorkflowService } from './workflow.service';
import {
  CreateWorkflowDto,
  ListWorkflowDto,
  UpdateWorkflowDto,
  ListUserAuditorsBodyDto,
  ListUserWorkflowTypePermissionDto,
  ListStepWorkflowTypePermissionDto,
} from './dto';
import { ListActiveUserPermissionsDto } from './dto';
import { ActionEnum, FeatureEnum, SubFeatureEnum } from '../../commons/enums';
import { ListRolePermissionDto } from '../iam/dto';
import { ListUserAuditorsInCompaniesBodyDto } from './dto/list-user-auditors-in-companies-body.dto';
import { ListQueryDto } from 'src/commons/dtos';

@ApiTags('Workflow configuration')
@Controller('workflow')
@ApiBearerAuth()
@UseGuards(AuthGuard, RolesGuard)
export class WorkflowController {
  constructor(private readonly workflowService: WorkflowService) {}

  @ApiResponse({ description: 'Create work flow success', status: HttpStatus.BAD_REQUEST })
  @ApiOperation({ summary: 'Create work flow' })
  @ApiBody({ type: CreateWorkflowDto, description: 'Create work flow object body' })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.WORKFLOW_CONFIGURATION,
    action: ActionEnum.CREATE,
  })
  @Post('')
  async createWorkflow(
    @TokenDecorator() user: TokenPayloadModel,
    @Body() body: CreateWorkflowDto,
    @I18n() i18n: I18nContext,
  ) {
    await this.workflowService.createWorkflow(user, body);
    return { message: await i18n.t('workflow.CREATE_SUCCESS') };
  }

  @ApiResponse({
    description: 'list active workflow permissions for current user success',
    status: HttpStatus.BAD_REQUEST,
  })
  @ApiOperation({ summary: 'Get list active permissions' })
  @ApiQuery({
    description: 'Paginate params',
    type: ListActiveUserPermissionsDto,
    required: false,
  })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @Get('/active-user-permission')
  async listActiveUserPermissions(
    @TokenDecorator() user: TokenPayloadModel,
    @Query() query: ListActiveUserPermissionsDto,
  ) {
    return await this.workflowService.listWorkflowPermissionByUser(user.id, {
      workflowType: query.workflowType,
      companyId: user.companyId,
    } as ListRolePermissionDto);
  }

  @ApiResponse({ description: 'List work flow success', status: HttpStatus.BAD_REQUEST })
  @ApiOperation({ summary: 'List work flow' })
  @ApiQuery({
    description: 'Paginate params',
    type: ListWorkflowDto,
    required: false,
  })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @Get('')
  @RequiredPermissions({
    feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.WORKFLOW_CONFIGURATION,
    action: ActionEnum.VIEW,
  })
  async listWorkflow(@TokenDecorator() user: TokenPayloadModel, @Query() query: ListWorkflowDto) {
    return await this.workflowService.listWorkflow(user, query);
  }

  @ApiResponse({ description: 'Get detail work flow success', status: HttpStatus.BAD_REQUEST })
  @ApiOperation({ summary: 'Get detail work flow' })
  @ApiParam({ name: 'id', type: 'string', required: true })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.WORKFLOW_CONFIGURATION,
    action: ActionEnum.VIEW,
  })
  @Get('/:id')
  async getDetailWorkflow(
    @TokenDecorator() user: TokenPayloadModel,
    @Param('id', ParseUUIDPipe) workflowId: string,
    @Query() query: ListQueryDto,
  ) {
    return await this.workflowService.getDetailWorkflow(user, workflowId, query);
  }

  @ApiResponse({ description: 'Delete work flow success', status: HttpStatus.BAD_REQUEST })
  @ApiOperation({ summary: 'Delete work flow' })
  @ApiParam({ name: 'id', type: 'string', required: true })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.WORKFLOW_CONFIGURATION,
    action: ActionEnum.DELETE,
  })
  @Delete('/:id')
  async deleteWorkflow(
    @TokenDecorator() user: TokenPayloadModel,
    @Param('id', ParseUUIDPipe) workflowId: string,
    @I18n() i18n: I18nContext,
  ) {
    await this.workflowService.deleteWorkflow(user, workflowId);
    return { message: await i18n.t('workflow.DELETE_SUCCESS') };
  }

  @ApiResponse({ description: 'Update work flow success', status: HttpStatus.BAD_REQUEST })
  @ApiOperation({ summary: 'Update work flow' })
  @ApiParam({ name: 'id', type: 'string', required: true })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.WORKFLOW_CONFIGURATION,
    action: ActionEnum.UPDATE,
  })
  @Put('/:id')
  async updateWorkflow(
    @TokenDecorator() user: TokenPayloadModel,
    @Param('id', ParseUUIDPipe) workflowId: string,
    @Body() body: UpdateWorkflowDto,
    @I18n() i18n: I18nContext,
  ) {
    await this.workflowService.updateWorkflow(user, workflowId, body);
    return { message: await i18n.t('workflow.UPDATE_SUCCESS') };
  }

  @ApiResponse({ description: 'List users are auditor', status: HttpStatus.BAD_REQUEST })
  @ApiOperation({ summary: 'List users are auditor' })
  @ApiBody({ type: ListUserAuditorsBodyDto })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @Post('/planning-request/auditors')
  async listUserAuditorsPR(
    @TokenDecorator() user: TokenPayloadModel,
    @Body() body: ListUserAuditorsBodyDto,
  ) {
    return await this.workflowService.listUserAuditorsPR(user, body);
  }

  @ApiResponse({
    description: 'List users are auditor in companies',
    status: HttpStatus.BAD_REQUEST,
  })
  @ApiOperation({ summary: 'List users are auditor in companies' })
  @ApiBody({ type: ListUserAuditorsInCompaniesBodyDto })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @Post('/planning-request/auditors-in-companies')
  async listUserAuditorsInCompanies(
    @TokenDecorator() user: TokenPayloadModel,
    @Body() body: ListUserAuditorsInCompaniesBodyDto,
  ) {
    return await this.workflowService.listUserAuditorsInCompanies(user, body);
  }

  @ApiResponse({
    description: 'List user workflow type and workflow permission success',
    status: HttpStatus.BAD_REQUEST,
  })
  @ApiOperation({ summary: 'List work flow' })
  @ApiQuery({
    type: ListUserWorkflowTypePermissionDto,
    required: false,
  })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @Get('/user/workflow-type-permission')
  async listUserWorkflowTypePermission(
    @TokenDecorator() user: TokenPayloadModel,
    @Query() query: ListUserWorkflowTypePermissionDto,
  ) {
    return await this.workflowService.listUserWorkflowTypePermission(user, query);
  }

  @ApiResponse({
    description: 'List step workflow type and workflow permission success',
    status: HttpStatus.BAD_REQUEST,
  })
  @ApiOperation({ summary: 'List work flow' })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @Get('/user/workflow-type-permission-step')
  async listStepWorkflowTypePermission(
    @TokenDecorator() user: TokenPayloadModel,
    @Query() query: ListStepWorkflowTypePermissionDto,
  ) {
    return await this.workflowService.listStepWorkflowTypePermission(user, query);
  }

  @ApiResponse({
    description: 'List step workflow type and workflow permission success',
    status: HttpStatus.BAD_REQUEST,
  })
  @ApiOperation({ summary: 'List work flow' })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @Post('/step-by-workflow')
  async getUserPermissionAuditChecklist(
    @TokenDecorator() user: TokenPayloadModel,
    @Body() body: ListUserWorkflowTypePermissionDto,
  ) {
    return this.workflowService.listUserByWorkflowType(user, body);
  }
}
