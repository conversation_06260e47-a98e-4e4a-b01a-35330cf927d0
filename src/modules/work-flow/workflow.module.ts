import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { WorkflowRepository } from './repository/workflow.repository';
import { WorkflowController } from './workflow.controller';
import { WorkflowService } from './workflow.service';
import { WorkflowRole } from './entities/work-flow-role.entity';
import { RoleRepository, UserRoleRepository } from '../iam/repository/role.repository';
import { MicroservicesSyncModule } from '../../micro-services/sync';
import { InternalWorkflowController } from './workflow-internal.controller';
@Module({
  imports: [
    TypeOrmModule.forFeature([
      WorkflowRepository,
      WorkflowRole,
      RoleRepository,
      UserRoleRepository,
    ]),
    MicroservicesSyncModule,
  ],
  controllers: [WorkflowController, InternalWorkflowController],
  providers: [WorkflowService],
  exports: [WorkflowService],
})
export class WorkflowModule {}
