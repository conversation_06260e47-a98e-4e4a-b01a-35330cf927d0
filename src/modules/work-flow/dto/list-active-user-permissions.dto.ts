import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty } from 'class-validator';
import { ListQueryDto } from '../../../commons/dtos';
import { WorkflowType } from '../../../commons/enums';

export class ListActiveUserPermissionsDto extends ListQueryDto {
  @ApiProperty({
    enum: WorkflowType,
    required: true,
    description: 'Workflow type',
  })
  @IsEnum(WorkflowType)
  @IsNotEmpty()
  workflowType: string;
}
