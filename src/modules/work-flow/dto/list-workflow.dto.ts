import { ListQueryDto } from '../../../commons/dtos';
import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional } from 'class-validator';
import { ApproverType, WorkflowType } from '../../../commons/enums';

export class ListWorkflowDto extends ListQueryDto {
  @ApiProperty({
    enum: ApproverType,
    required: false,
    description: 'approver type',
  })
  @IsOptional()
  @IsEnum(ApproverType)
  approverType?: string;

  @ApiProperty({
    enum: WorkflowType,
    required: false,
    description: 'Workflow type',
  })
  @IsNotEmpty()
  @IsEnum(WorkflowType)
  workflowType: string;
}
