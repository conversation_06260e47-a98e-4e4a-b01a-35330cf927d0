import { PartialType } from '@nestjs/mapped-types';
import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsUUID } from 'class-validator';
import { CreateWorkflowRoleDto } from './create-workflow-role.dto';

export class UpdateWorkflowRoleDto extends PartialType(CreateWorkflowRoleDto) {
  @ApiProperty({ type: 'string', example: '740d2d97-1144-49f7-8839-7fe919d20a78', required: false })
  @IsOptional()
  @IsUUID('all')
  id?: string;
}
