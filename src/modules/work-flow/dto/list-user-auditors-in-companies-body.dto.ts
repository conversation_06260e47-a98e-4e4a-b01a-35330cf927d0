import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { Allow, ArrayUnique, IsArray, IsOptional, IsUUID } from 'class-validator';

export class ListUserAuditorsInCompaniesBodyDto {
  @ApiProperty({ type: 'string' })
  @IsOptional()
  @IsUUID()
  companyParentId?: string;

  @ApiProperty({
    type: [String],
    name: 'childCompanyIds',
    required: false,
    description: 'childCompanyIds',
  })
  @IsOptional()
  @IsArray()
  @IsUUID('all', { each: true })
  @ArrayUnique()
  childCompanyIds?: string[] = [];

  @ApiProperty({
    type: 'string',
    required: false,
    description: 'Search key',
  })
  @Allow()
  @Transform((value: string) => value.trim().replace(/%/g, '\\%'))
  content?: string; // search key
}
