import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, IsEnum, IsNotEmpty, IsOptional, ValidateNested } from 'class-validator';
import { WorkflowType } from '../../../commons/enums';
import { CreateWorkflowRoleDto } from './create-workflow-role.dto';
export class CreateWorkflowDto {
  @ApiProperty({ enum: WorkflowType })
  @IsNotEmpty()
  @IsEnum(WorkflowType)
  workflowType: string;

  @ApiProperty({ required: false })
  @IsOptional()
  description?: string;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateWorkflowRoleDto)
  @ApiProperty({
    type: [CreateWorkflowRoleDto],
    description: 'work flow roles',
    required: false,
  })
  workflowRoles?: CreateWorkflowRoleDto[] = [];
}
