import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';
import { WorkflowPermission } from './../../../commons/enums';

export class CreateWorkflowRoleDto {
  @ApiProperty({ required: true })
  @IsOptional()
  roleId?: string;

  @ApiProperty({
    enum: WorkflowPermission,
    required: false,
    description: 'permission work flow role',
  })
  @IsOptional()
  @IsEnum(WorkflowPermission)
  permission?: string;
}
