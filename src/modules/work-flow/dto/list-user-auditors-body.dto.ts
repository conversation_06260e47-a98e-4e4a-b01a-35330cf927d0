import { ApiProperty } from '@nestjs/swagger';
import { ArrayMinSize, ArrayUnique, IsArray, IsBoolean, IsEnum, IsOptional, IsUUID } from 'class-validator';
import { EntityTypePlanningRequest } from '../../../commons/enums';

export class ListUserAuditorsBodyDto {
  @ApiProperty({
    type: [String],
    name: 'inspectionTypeIds',
    required: false,
    description: 'List inspectionType Ids',
  })
  @IsOptional()
  @IsArray()
  @IsUUID('all', { each: true })
  @ArrayUnique()
  @ArrayMinSize(1)
  inspectionTypeIds?: string[];

  @ApiProperty({ type: 'string' })
  @IsOptional()
  @IsUUID()
  vesselId?: string;

  @ApiProperty({
    enum: EntityTypePlanningRequest,
    required: false,
    description: 'Enum entity type planning request',
  })
  @IsOptional()
  @IsEnum(EntityTypePlanningRequest)
  entityType?: string;

  @ApiProperty({ type: 'string' })
  @IsOptional()
  @IsUUID()
  companyId?: string;

  @ApiProperty({ type: 'string' })
  @IsOptional()
  @IsUUID()
  inspectorId?: string;
  
  @ApiProperty({ type: Boolean, required: false })
  @IsOptional()
  @IsBoolean()
  isSA?: boolean;
}
