import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';
import { WorkflowType, WorkflowPermission } from '../../../commons/enums';
export class ListStepWorkflowTypePermissionDto {
  @ApiProperty({
    enum: WorkflowType,
    required: false,
    description: 'workflow type',
  })
  @IsOptional()
  @IsEnum(WorkflowType)
  workflowType?: string;

  @ApiProperty({
    enum: WorkflowPermission,
    required: false,
    description: 'workflow permission',
  })
  @IsOptional()
  @IsEnum(WorkflowPermission)
  workflowPermission?: string;
}
