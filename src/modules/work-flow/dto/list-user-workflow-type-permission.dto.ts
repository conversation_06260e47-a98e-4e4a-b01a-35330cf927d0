import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsUUID } from 'class-validator';
import { WorkflowType, WorkflowPermission } from '../../../commons/enums';
export class ListUserWorkflowTypePermissionDto {
  @ApiProperty({
    enum: WorkflowType,
    required: false,
    description: 'workflow type',
  })
  @IsOptional()
  @IsEnum(WorkflowType)
  workflowType?: string;

  @ApiProperty({
    enum: WorkflowPermission,
    required: false,
    description: 'workflow permission',
  })
  @IsOptional()
  @IsEnum(WorkflowPermission)
  workflowPermission?: string;

  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  @IsUUID()
  vesselId?: string;

  @ApiProperty({ type: String, required: false })
  @IsOptional()
  @IsUUID()
  recordId?: string;
}
