import { PartialType } from '@nestjs/mapped-types';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, IsEnum, IsNotEmpty, IsOptional, ValidateNested } from 'class-validator';
import { WorkflowType } from '../../../commons/enums';
import { CreateWorkflowDto } from './create-workflow.dto';
import { UpdateWorkflowRoleDto } from './update-workflow-role.dto';

export class UpdateWorkflowDto extends PartialType(CreateWorkflowDto) {
  @ApiProperty({ enum: WorkflowType })
  @IsNotEmpty()
  @IsEnum(WorkflowType)
  workflowType: string;

  @ApiProperty({ required: false })
  @IsOptional()
  description?: string;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UpdateWorkflowRoleDto)
  @ApiProperty({
    type: [UpdateWorkflowRoleDto],
    description: 'work flow roles',
    required: false,
  })
  workflowRoles?: UpdateWorkflowRoleDto[] = [];
}
