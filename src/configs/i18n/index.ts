import * as path from 'path';
import { Module } from '@nestjs/common';
import {
  I18nModule,
  QueryResolver,
  CookieResolver,
  HeaderResolver,
  I18nJsonParser,
  AcceptLanguageResolver,
} from 'nestjs-i18n';
import APP_CONFIG from '../app.config';

@Module({
  imports: [
    I18nModule.forRoot({
      fallbackLanguage: 'en',
      fallbacks: {
        'en-*': 'en',
        'vi-*': 'vi',
      },
      parser: I18nJsonParser,
      parserOptions: {
        path: path.join(APP_CONFIG.ROOT, '/configs/i18n/'),
        watch: true,
      },
      resolvers: [
        { use: QueryResolver, options: ['lang', 'locale', 'l'] },
        new HeaderResolver(['x-custom-lang']),
        AcceptLanguageResolver,
        new CookieResolver(['lang', 'locale', 'l']),
      ],
    }),
  ],
})
export class I18nConfigModule {}
