import { ifError } from 'assert';
import * as childProcess from 'child_process';
import * as chokidar from 'chokidar';
import { Environment, registerExtensionMethods } from 'svm-nest-lib';

/**
 * @method preloadExtensionMethods
 * @description load extension method
 */
function preloadExtensionMethods() {
  registerExtensionMethods({ pagination: true, response: true });
}

function watchConfigChange() {
  chokidar
    .watch(`${global.__CONFIGMAP_PATH}/**/*configmap.yaml`, {
      ignoreInitial: true,
    })
    .on('change', () => {
      setTimeout(() => {
        childProcess.exec('pm2 restart all', {}, (err, stderr, stdout) => {
          console.log(err, stderr, stdout);
          console.log('New config map apply at: ', new Date());
        });
      }, 2000);
    });
}

export default async () => {
  /** watch change process*/
  if (process.env.NODE_ENV !== Environment.LOCAL) {
    watchConfigChange();
  }
  // Init s3 bucket
  // S3Service.createBucket(APP_CONFIG.ENV.AWS.S3.BUCKET);
  preloadExtensionMethods();
};
