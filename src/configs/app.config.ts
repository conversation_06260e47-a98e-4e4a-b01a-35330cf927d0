/**
 * @class AppConfig
 * @description config environment + preload bootstrap app
 * @since 1.0.0
 */

import * as path from 'path';
import * as glob from 'glob';
import * as YAML from 'yamljs';

import { Environment } from 'svm-nest-lib';
import { LoggerCommon } from 'svm-nest-lib';
import { AppConfigModel, AppEnvironment } from '../commons/interfaces';

const ROOT = path.normalize(__dirname + '/..');

function setupEnv(): AppEnvironment {
  const mode = (process.env.NODE_ENV || process.argv[2] || Environment.PRODUCTION).trim();
  // set environment
  process.env.NODE_ENV = mode;

  LoggerCommon.log(`Application loaded using the "${mode}" environment configuration.`);
//  let env: AppEnvironment;
//  if ([Environment.LOCAL, Environment.TEST].indexOf(mode as Environment) > -1) {
  const  env = YAML.load(path.join(ROOT, `/configs/environment/${mode}.env.yaml`));
//  } else {
//    env = glob
//      .sync(path.normalize(`${global.__CONFIGMAP_PATH}/**/*configmap.yaml`))
//      .reduce((envTemp: any, fileConfig: string) => {
//        return { ...envTemp, ...YAML.load(fileConfig) };
//      }, {});
//  }
  return env as AppEnvironment;
}

const ENV: AppEnvironment = setupEnv();

export default {
  ROOT: ROOT,
  ENV: ENV,
} as AppConfigModel;
