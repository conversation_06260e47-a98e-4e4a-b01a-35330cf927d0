import { initConfig, ConfigLibModel } from 'svm-nest-lib';

import APP_CONFIG from './app.config';
import { AppConst } from '../commons/consts/app.const';

export function initConfigLib() {
  const configDataLib: ConfigLibModel = {
    ENVIRONMENT: process.env.NODE_ENV,
    PAGE_SIZE: AppConst.PAGE_SIZE,
    SECURE: {
      JWT: {
        TOKEN_EXPIRE: APP_CONFIG.ENV.SHARE.SECURE.JWT.TOKEN_EXPIRE,
        JWT_SECRET: APP_CONFIG.ENV.SHARE.SECURE.JWT.JWT_SECRET,
        FIELD: [],
      },
      API_RESTRICT: {
        CLIENT_SECRET: APP_CONFIG.ENV.SHARE.SECURE.API_RESTRICT.CLIENT_SECRET,
      },
      GATEWAY_CONFIG: {
        METHOD: APP_CONFIG.ENV.SHARE.SECURE.GATEWAY_CONFIG.METHOD,
        HOST: APP_CONFIG.ENV.SHARE.SECURE.GATEWAY_CONFIG.HOST,
      },
    },
  };
  initConfig(configDataLib);
}
