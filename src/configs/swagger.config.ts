import { INestApplication } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { Environment } from 'svm-nest-lib';
import APP_CONFIG from './app.config';

export default function initSwagger(server: INestApplication) {
  // Init api swagger if neither 'production' nor 'test" environment
  if (
    [Environment.TEST, Environment.PRODUCTION].indexOf(process.env.NODE_ENV as Environment) === -1
  ) {
    const baseUrl =
      Environment.LOCAL === process.env.NODE_ENV
        ? `http://localhost:${APP_CONFIG.ENV.APP.PORT}`
        : `${APP_CONFIG.ENV.SHARE.PUBLIC.GATEWAY_CONFIG.METHOD}://${APP_CONFIG.ENV.SHARE.PUBLIC.GATEWAY_CONFIG.HOST}/${APP_CONFIG.ENV.APP.NAME}`;

    const apiDocOptions = new DocumentBuilder()
      .setTitle(`SMV INautix ${APP_CONFIG.ENV.APP.NAME} API`)
      .setDescription(`The SMV INautix ${APP_CONFIG.ENV.APP.NAME} API documentation`)
      .setVersion('1.0')
      .addBearerAuth()
      .addServer(baseUrl, 'BASE URL')
      .build();

    const document = SwaggerModule.createDocument(server, apiDocOptions, {
      operationIdFactory: (controllerKey: string, methodKey: string) => methodKey,
    });
    SwaggerModule.setup('docs', server, document);
  }
}
