SHARE:
  PUBLIC:
    GATEWAY_CONFIG:
      METHOD: https
      HOST: api.staging.i-nautix.com
    POLICY_INFO:
      TERMS_OF_SERVICE: https://www.solverminds.com/terms-of-use/
      CONTACT:
        NAME: 'Solverminds'
        URL: https://www.solverminds.sg/contact-us
        EMAIL: <EMAIL>
      LICENSE:
        NAME: 'LICENSE- SVM'
        URL: https://www.solverminds.sg/about-us
    RESOURCE:
      CDN_RESOURCE: 'https://svm-inautix-staging.s3.amazonaws.com'
      HOME_PAGE: 'https://staging.i-nautix.com'
      ADMIN_PAGE: 'https://admin.staging.i-nautix.com'
  SECURE:
    CORS:
      ORIGIN:
        - 'https://admin.staging.i-nautix.com'
        - 'https://api.staging.i-nautix.com'
        - 'https://staging.i-nautix.com'
      METHODS:
        - 'GET'
        - 'PUT'
        - 'POST'
        - 'DELETE'
        - 'PATCH'
        - 'HEAD'
        - 'OPTIONS'
      ALLOWED_HEADERS:
        - '*'
      EXPOSED_HEADERS:
        - '*'
      CREDENTIALS: true
      PREFLIGHT_CONTINUE: false
    JWT:
      JWT_SECRET: 'inautix-staging-ship2938232-c2##2021'
      TOKEN_EXPIRE: 604800
    API_RESTRICT:
      CLIENT_SECRET: 't8$Udfs9E2s-f34vkl2$91232MLY20212022410322'
    KEYS:
      ENCRYPT_KEY: "97bbd1130080079f4a6f1745b591b303"
      ENCRYPT_KEY_MOBILE: '#d@g$H907845%^U@'
    GATEWAY_CONFIG:
      METHOD: https
      HOST: api.staging.i-nautix.com
NAME: local
APP:
  NAME: iam
  PORT: 8181
  IP: 0.0.0.0
DATABASE:
  POSTGRES:
    USERNAME: 'svmassetsstaging'
    PASSWORD: 'P@8KS[XGrDNRAdf'
    HOST: 'db210826x-development.i-nautix.com'
    PORT: 5432
    NAME: 'svm-assets-staging'
    # USERNAME: svmassetsstaging
    # PASSWORD: 'VK[%YfZ5{::7WNv'
    # HOST: db210826x-stagingelopment.i-nautix.com
    # PORT: 5432
    # NAME: svm-assets-staging
  REDIS:
    HOST: '127.0.0.1'
    PORT: 6379
    PASSWORD: ''
    DB: 0
    KEY_PREFIX: ''
NETWORK:
  XXX_API_KEYS:
    - xxx

