SHARE:
  PUBLIC:
    GATEWAY_CONFIG:
      METHOD: https
      HOST: api.dev.i-nautix.com
    POLICY_INFO:
      TERMS_OF_SERVICE: https://www.solverminds.com/terms-of-use/
      CONTACT:
        NAME: 'Solverminds'
        URL: https://www.solverminds.sg/contact-us
        EMAIL: <EMAIL>
      LICENSE:
        NAME: 'LICENSE- SVM'
        URL: https://www.solverminds.sg/about-us
    RESOURCE:
      CDN_RESOURCE: "https://svm-inautix-dev.s3.amazonaws.com"
      HOME_PAGE: "https://dev.i-nautix.com"
      ADMIN_PAGE: "https://admin.dev.i-nautix.com"
  SECURE:
    CORS:
      ORIGIN:
        - '*'
      METHODS:
        - 'GET'
        - 'PUT'
        - 'POST'
        - 'DELETE'
        - 'PATCH'
        - 'HEAD'
        - 'OPTIONS'
      ALLOWED_HEADERS:
        - '*'
      EXPOSED_HEADERS:
        - '*'
      CREDENTIALS: true
      PREFLIGHT_CONTINUE: false
    JWT:
      JWT_SECRET: 'inautix-asxfghfs-c2##2021'
      TOKEN_EXPIRE: 604800
    API_RESTRICT:
      CLIENT_SECRET: 'sa$UGfs9s2s-234akl#$3'
    GATEWAY_CONFIG:
      METHOD: https
      HOST: api.dev.i-nautix.com
NAME: local
APP:
  NAME: iam
  PORT: 7171
  IP: 0.0.0.0
DATABASE:
  POSTGRES:
    USERNAME: 'postgres'
    PASSWORD: 'postgres'
    HOST: localhost
    PORT: 5432
    NAME: svm-iam-test
  REDIS:
    HOST: 'localhost'
    PORT: 6379
    PASSWORD: ''
    DB: 0
    KEY_PREFIX: ''
NETWORK:
  XXX_API_KEYS:
    - xxx
