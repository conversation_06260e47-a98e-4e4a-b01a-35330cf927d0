SHARE:
  PUBLIC:
    GATEWAY_CONFIG:
      METHOD: https
      HOST: api.uat.i-nautix.com
    POLICY_INFO:
      TERMS_OF_SERVICE: https://www.solverminds.com/terms-of-use/
      CONTACT:
        NAME: 'Solverminds'
        URL: https://www.solverminds.sg/contact-us
        EMAIL: <EMAIL>
      LICENSE:
        NAME: 'LICENSE- SVM'
        URL: https://www.solverminds.sg/about-us
    RESOURCE:
      CDN_RESOURCE: 'https://svm-inautix-uat.s3.amazonaws.com'
      HOME_PAGE: 'https://uat.i-nautix.com'
      ADMIN_PAGE: 'https://admin.uat.i-nautix.com'
  SECURE:
    CORS:
      ORIGIN:
        - 'https://admin.uat.i-nautix.com'
        - 'https://api.uat.i-nautix.com'
        - 'https://uat.i-nautix.com'
      METHODS:
        - 'GET'
        - 'PUT'
        - 'POST'
        - 'DELETE'
        - 'PATCH'
        - 'HEAD'
        - 'OPTIONS'
      ALLOWED_HEADERS:
        - '*'
      EXPOSED_HEADERS:
        - '*'
      CREDENTIALS: true
      PREFLIGHT_CONTINUE: false
    JWT:
      JWT_SECRET: 'inautix-uat-ship2938232-c2##2021'
      TOKEN_EXPIRE: 604800
    API_RESTRICT:
      CLIENT_SECRET: 't8$Udfs9E2s-f34vkl2$91232MLY20212022410322'
    KEYS:
      ENCRYPT_KEY: "97bbd1130080079f4a6f1745b591b303"
      ENCRYPT_KEY_MOBILE: '#d@g$H907845%^U@'
    GATEWAY_CONFIG:
      METHOD: https
      HOST: api.uat.i-nautix.com
NAME: local
APP:
  NAME: iam
  PORT: 8181
  IP: 0.0.0.0
DATABASE:
  POSTGRES:
    USERNAME: 'svmassetsuat'
    PASSWORD: 'B%5:)$6KqU+iCn:i'
    HOST: 'db210826x-development.i-nautix.com'
    PORT: 5432
    NAME: 'svm-assets-uat'
    # USERNAME: svmassetsuat
    # PASSWORD: 'VK[%YfZ5{::7WNv'
    # HOST: db210826x-uatelopment.i-nautix.com
    # PORT: 5432
    # NAME: svm-assets-uat
  REDIS:
    HOST: '127.0.0.1'
    PORT: 6379
    PASSWORD: ''
    DB: 0
    KEY_PREFIX: ''
NETWORK:
  XXX_API_KEYS:
    - xxx

