SHARE:
  PUBLIC:
    GATEWAY_CONFIG:
      METHOD: https
      HOST: api.testing.i-nautix.com
    POLICY_INFO:
      TERMS_OF_SERVICE: https://www.solverminds.com/terms-of-use/
      CONTACT:
        NAME: 'Solverminds'
        URL: https://www.solverminds.sg/contact-us
        EMAIL: <EMAIL>
      LICENSE:
        NAME: 'LICENSE- SVM'
        URL: https://www.solverminds.sg/about-us
    RESOURCE:
      CDN_RESOURCE: 'https://svm-inautix-testing.s3.amazonaws.com'
      HOME_PAGE: 'https://testing.i-nautix.com'
      ADMIN_PAGE: 'https://admin.testing.i-nautix.com'
  SECURE:
    CORS:
      ORIGIN:
        - '*'
      METHODS:
        - 'GET'
        - 'PUT'
        - 'POST'
        - 'DELETE'
        - 'PATCH'
        - 'HEAD'
        - 'OPTIONS'
      ALLOWED_HEADERS:
        - '*'
      EXPOSED_HEADERS:
        - '*'
      CREDENTIALS: true
      PREFLIGHT_CONTINUE: false
    JWT:
      JWT_SECRET: 'inautix-uat-ship2938232-c2##2021'
      TOKEN_EXPIRE: 604800
    API_RESTRICT:
      CLIENT_SECRET: 't8$Udfs9E2s-f34vkl2$91232MLY20212022410322'
    KEYS:
      ENCRYPT_KEY: "97bbd1130080079f4a6f1745b591b303"
      ENCRYPT_KEY_MOBILE: '#d@g$H907845%^U@'
    GATEWAY_CONFIG:
      METHOD: https
      HOST: api.testing.i-nautix.com
NAME: local
APP:
  NAME: iam
  PORT: 8181
  IP: 0.0.0.0
DATABASE:
  POSTGRES:
    USERNAME: 'postgres'
    PASSWORD: 'svminau21x#150'
    HOST: 'db210826x-development.i-nautix.com'
    PORT: 5432
    NAME: 'svm-assets-test'
    # USERNAME: svmassetstesting
    # PASSWORD: 'VK[%YfZ5{::7WNv'
    # HOST: db210826x-testingelopment.i-nautix.com
    # PORT: 5432
    # NAME: svm-assets-testing
  REDIS:
    HOST: 'redis-service'
    PORT: '6379'
    PASSWORD: ''
    DB: 0
    KEY_PREFIX: ''
NETWORK:
  XXX_API_KEYS:
    - xxx
