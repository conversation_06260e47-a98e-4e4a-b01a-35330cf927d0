import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { Allow, IsEnum, IsInt, IsOptional, IsUUID } from 'class-validator';
import { StatusCommon } from '../enums';

export class ListQueryDto {
  @ApiProperty({
    type: 'number',
    required: false,
    description: 'Page number of list',
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  page?: number;

  @ApiProperty({
    type: 'number',
    required: false,
    description: 'Page size number of list',
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  pageSize?: number;

  @ApiProperty({
    type: 'string',
    required: false,
    description: 'Search key',
  })
  @Allow()
  content?: string; // search key

  @ApiProperty({
    enum: StatusCommon,
    required: false,
    description: 'Status in common',
  })
  @IsOptional()
  @IsEnum(StatusCommon)
  status?: string; // common status

  @ApiProperty({
    type: 'string',
    required: false,
    description: 'Sort of list, separate by semi-colon. Example: fields1:1;fields2:-1',
  })
  @Allow()
  sort?: string;

  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  @IsUUID('all')
  companyId?: string;
}

// export interface IListQueryDto extends ListQueryDto {}
