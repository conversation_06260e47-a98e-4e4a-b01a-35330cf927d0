export * from './role-permission.enum';
export * from './workflow-permission.enum';

export enum StatusCommon {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

export enum DataChangeEvent {
  INSERT = 'insert',
  UPDATE = 'update',
  DELETE = 'delete',
}

export enum EntityTypePlanningRequest {
  VESSEL = 'Vessel',
  OFFICE = 'Office',
}

export enum CompanyLevelEnum {
  MAIN_COMPANY = 'Main Company',
  INTERNAL_COMPANY = 'Internal Company',
  EXTERNAL_COMPANY = 'External Company',
}

export enum PackageEnum {
  INSPECTIONS_PACKAGE = 'Inspections Package',
  QA_PACKAGE = 'QA Package',
  INSPECTIONS_AND_QA_PACKAGE = 'Inspections And QA Package',
}

export enum CompanyTypeEnum {
  SHIP_MAGEMENT = 'Ship management (DOC holder)',
  SHIP_OWNER = 'Ship Owner',
  CHARTERER = 'Charterer',
  PILOT_SERVICES = 'Pilot Services',
  TERMINAL = 'Terminal',
  INSPECTION_SERVICES = 'Inspection Services',
  SERVICE_PROVIDER = 'Service Provider',
  OTHERS = 'Others',
}
