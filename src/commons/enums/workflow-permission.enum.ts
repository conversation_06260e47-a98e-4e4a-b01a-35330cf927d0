export enum WorkflowType {
  AUDIT_CHECKLIST = 'Audit checklist',
  PLANNING_REQUEST = 'Planning request',
  REPORT_FINDING = 'Report finding',
  CORRECTIVE_ACTION_REQUEST = 'CAR/CAP', // Check with role-permission: Insp Follow Up _ Execute
  INTERNAL_AUDIT_REPORT = 'Internal audit report',
  SELF_ASSESSMENT = 'Self assessment',
  INCIDENTS = 'Incidents',
}

export enum WorkflowPermission {
  CREATOR = 'creator', // Audit Checklist, PR, ROF, IAR, CAR/CAP, Self assessment
  APPROVER = 'approver', // Audit Checklist, PR, IAR, ROF
  REVIEWER = 'reviewer', // Audit Checklist, ROF, CAR/CAP, Self assessment
  AUDITOR = 'auditor', // PR (acceptor - who will accept or reject PR)
  OWNER_MANAGER = 'owner/manager', // PR (vessel owner, department manager)
  CLOSE_OUT = 'close_out', // ROF
  REVIEWER1 = 'reviewer1', // IAR, Audit Checklist, PR, ROF, CAR/CAP
  REVIEWER2 = 'reviewer2', // IAR, Audit Checklist, PR, ROF, CAR/CAP
  REVIEWER3 = 'reviewer3', // IAR, Audit Checklist, PR, ROF, CAR/CAP
  REVIEWER4 = 'reviewer4', // IAR, Audit Checklist, PR, ROF, CAR/CAP
  REVIEWER5 = 'reviewer5', // IAR, Audit Checklist, PR, ROF, CAP/CAP
  VERIFICATION = 'verification', // CAR/CAP
  PUBLISHER = 'publisher', //Self assessment
}

export const WORKFLOW_TYPE_MAP_PERMISSIONS = {
  [WorkflowType.AUDIT_CHECKLIST]: [
    WorkflowPermission.CREATOR,
    WorkflowPermission.APPROVER,
    WorkflowPermission.REVIEWER,
    WorkflowPermission.REVIEWER1,
    WorkflowPermission.REVIEWER2,
    WorkflowPermission.REVIEWER3,
    WorkflowPermission.REVIEWER4,
    WorkflowPermission.REVIEWER5,
  ],

  [WorkflowType.PLANNING_REQUEST]: [
    WorkflowPermission.CREATOR,
    WorkflowPermission.APPROVER,
    WorkflowPermission.AUDITOR,
    WorkflowPermission.OWNER_MANAGER,
    WorkflowPermission.REVIEWER1,
    WorkflowPermission.REVIEWER2,
    WorkflowPermission.REVIEWER3,
    WorkflowPermission.REVIEWER4,
    WorkflowPermission.REVIEWER5,
  ],

  [WorkflowType.REPORT_FINDING]: [
    WorkflowPermission.CREATOR,
    WorkflowPermission.REVIEWER,
    WorkflowPermission.REVIEWER1,
    WorkflowPermission.REVIEWER2,
    WorkflowPermission.REVIEWER3,
    WorkflowPermission.REVIEWER4,
    WorkflowPermission.REVIEWER5,
    WorkflowPermission.APPROVER,
    WorkflowPermission.CLOSE_OUT,
  ],

  [WorkflowType.CORRECTIVE_ACTION_REQUEST]: [
    WorkflowPermission.CREATOR,
    WorkflowPermission.REVIEWER,
    WorkflowPermission.REVIEWER1,
    WorkflowPermission.REVIEWER2,
    WorkflowPermission.REVIEWER3,
    WorkflowPermission.REVIEWER4,
    WorkflowPermission.REVIEWER5,
    WorkflowPermission.VERIFICATION,
  ],

  [WorkflowType.INTERNAL_AUDIT_REPORT]: [
    WorkflowPermission.CREATOR,
    WorkflowPermission.REVIEWER1,
    WorkflowPermission.REVIEWER2,
    WorkflowPermission.REVIEWER3,
    WorkflowPermission.REVIEWER4,
    WorkflowPermission.REVIEWER5,
    WorkflowPermission.APPROVER,
  ],

  [WorkflowType.SELF_ASSESSMENT]: [
    WorkflowPermission.CREATOR,
    WorkflowPermission.REVIEWER,
    WorkflowPermission.PUBLISHER,
  ],
  [WorkflowType.INCIDENTS]: [WorkflowPermission.CREATOR, WorkflowPermission.REVIEWER],
};

export enum WorkflowStatus {
  PUBLISHED = 'Published',
  INACTIVE = 'Inactive',
}

export enum ApproverType {
  WITHOUT_BUDGET_AMOUNT = 'Without budget amount',
}
