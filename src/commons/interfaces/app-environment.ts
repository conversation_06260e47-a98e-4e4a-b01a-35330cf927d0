export interface AppEnvironment {
  SHARE: {
    PUBLIC: {
      GATEWAY_CONFIG: {
        METHOD: string;
        HOST: string;
      };
      POLICY_INFO: {
        TERMS_OF_SERVICE: string;
        CONTACT: {
          NAME: string;
          URL: string;
          EMAIL: string;
        };
        LICENSE: {
          NAME: string;
          URL: string;
        };
      };
      RESOURCE: {
        CDN_RESOURCE: string;
        HOME_PAGE: string;
        ADMIN_PAGE: string;
      };
    };
    SECURE: {
      CORS: {
        ORIGIN: string[];
        METHODS: string[];
        ALLOWED_HEADERS: string[];
        EXPOSED_HEADERS: string[];
        CREDENTIALS: boolean;
        PREFLIGHT_CONTINUE: boolean;
      };
      JWT: {
        JWT_SECRET: string;
        TOKEN_EXPIRE: number;
      };
      API_RESTRICT?: {
        CLIENT_SECRET?: string;
      };
      GATEWAY_CONFIG?: {
        METHOD: string;
        HOST: string;
      };
    };
  };
  // In app info
  NAME: string;
  APP: {
    NAME: string;
    PORT: number;
    IP: string;
  };
  DATABASE: {
    POSTGRES: {
      USERNAME: string;
      PASSWORD: string;
      HOST: string;
      PORT: number;
      NAME: string;
    };
    REDIS: {
      HOST: string;
      PORT: number;
      PASSWORD: string;
      DB: number;
      KEY_PREFIX: string;
    };
  };
  NETWORK?: {
    XXX_API_KEYS: string[];
  };
}
