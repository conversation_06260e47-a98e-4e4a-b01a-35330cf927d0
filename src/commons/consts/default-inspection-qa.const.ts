import { ROLE_NAME_DEFAULT } from '../../modules/iam/config';
import { ActionEnum, FeatureEnum, SubFeatureEnum } from '../enums';

export const DEFAULT_ROLES_INSPECTION_QA = [
  {
    name: ROLE_NAME_DEFAULT.COMMON.COMPANY_LOCAL_ADMIN,
    description: ROLE_NAME_DEFAULT.COMMON.COMPANY_LOCAL_ADMIN,
    permissions: [
      {
        feature: FeatureEnum.GROUP_COMPANY + '::' + SubFeatureEnum.COMPANY,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.USER_ROLE + '::' + SubFeatureEnum.ROLE_AND_PERMISSION,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.USER_ROLE + '::' + SubFeatureEnum.USER,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.DIVISION,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.DEPARTMENT_MASTER,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.VESSEL,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.VIQ,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.VESSEL_TYPE,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.WORKFLOW_CONFIGURATION,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.CHARTER_OWNER,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.DEVICE_CONTROL,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.MAIL_TEMPLATE,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.MOBILE_CONFIG,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.ANSWER_VALUE,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.AUTHORITY_MASTER,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.STANDARD_MASTER,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.ELEMENT_MASTER,
        actions: [ActionEnum.VIEW],
      },
      // adding new permissions for voyage status
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.VOYAGE_STATUS,
        actions: [ActionEnum.CREATE, ActionEnum.VIEW, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      //#CVIQ Masters
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.CVIQ_VERSION,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.CVIQ_CHAPTER,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.CVIQ_CONDITIONALITY,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.CVIQ_DETAILS_MAPPING,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      //#endregion CVIQ Masters
    ],
  },
  //# start: region inspection
  {
    name: ROLE_NAME_DEFAULT.INSPECTION.BPO_INSPECTION,
    description: ROLE_NAME_DEFAULT.INSPECTION.BPO_INSPECTION,
    permissions: [
      {
        feature: FeatureEnum.GROUP_COMPANY + '::' + SubFeatureEnum.COMPANY,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.DASHBOARD,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.MAP_VIEW,
        actions: [ActionEnum.VIEW],
      },
      // {
      //   feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.REPORT_TEMPLATE,
      //   actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      // },
      // {
      //   feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.AUDIT_CHECKLIST,
      //   actions: [ActionEnum.EXECUTE],
      // },
      // {
      //   feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.INSPECTION_MAPPING,
      //   actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      // },
      {
        feature: FeatureEnum.USER_ROLE + '::' + SubFeatureEnum.ROLE_AND_PERMISSION,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.USER_ROLE + '::' + SubFeatureEnum.USER,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.AUDIT_TYPE,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.LOCATION_MASTER,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.MAIN_CATEGORY,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.VESSEL,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.VIQ,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.VESSEL_TYPE,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.WORKFLOW_CONFIGURATION,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.PORT_MASTER,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.REPORT_TEMPLATE,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.AUDIT_CHECKLIST,
        actions: [ActionEnum.VIEW, ActionEnum.EXECUTE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.INSPECTION_MAPPING,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.CATEGORY_MAPPING,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.SECOND_CATEGORY,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.THIRD_CATEGORY,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.CHARTER_OWNER,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.CDI,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.DEVICE_CONTROL,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.FOCUS_REQUEST,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.MAIL_TEMPLATE,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.MOBILE_CONFIG,
        actions: [ActionEnum.VIEW],
      },
      {
        feature:
          FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.NATURE_OF_FINDINGS_MASTER,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.RANK_MASTER,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.TOPIC,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.ANSWER_VALUE,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE],
      },
      // adding new permissions for voyage status
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.VOYAGE_STATUS,
        actions: [ActionEnum.VIEW],
      },
    ],
  },
  {
    name: ROLE_NAME_DEFAULT.INSPECTION.INSPECTION_PLANNER,
    description: ROLE_NAME_DEFAULT.INSPECTION.INSPECTION_PLANNER,
    permissions: [
      {
        feature: FeatureEnum.GROUP_COMPANY + '::' + SubFeatureEnum.COMPANY,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.DASHBOARD,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.MAP_VIEW,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.PLANNING_AND_REQUEST,
        actions: [ActionEnum.VIEW, ActionEnum.EXPORT, ActionEnum.EXECUTE, ActionEnum.EMAIL],
      },

      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.AUDIT_INSPECTION_WORKSPACE,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.EXPORT],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.REPORT_OF_FINDING,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.INTERNAL_AUDIT_REPORT,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.INSPECTION_FOLLOW_UP,
        actions: [ActionEnum.VIEW],
      },

      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.VESSEL,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.PORT_MASTER,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.FOCUS_REQUEST,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.MOBILE_CONFIG,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.INSPECTOR_TIME_OFF,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE],
      },
      // adding new permissions for voyage status
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.VOYAGE_STATUS,
        actions: [ActionEnum.VIEW],
      },
    ],
  },
  {
    name: ROLE_NAME_DEFAULT.INSPECTION.INSPECTION_MANAGER,
    description: ROLE_NAME_DEFAULT.INSPECTION.INSPECTION_MANAGER,
    permissions: [
      {
        feature: FeatureEnum.GROUP_COMPANY + '::' + SubFeatureEnum.COMPANY,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.DASHBOARD,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.MAP_VIEW,
        actions: [ActionEnum.VIEW],
      },
      // {
      //   feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.REPORT_TEMPLATE,
      //   actions: [ActionEnum.VIEW],
      // },
      // {
      //   feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.AUDIT_CHECKLIST,
      //   actions: [ActionEnum.VIEW],
      // },
      // {
      //   feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.INSPECTION_MAPPING,
      //   actions: [ActionEnum.VIEW],
      // },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.PLANNING_AND_REQUEST,
        actions: [ActionEnum.VIEW, ActionEnum.EXPORT, ActionEnum.EXECUTE, ActionEnum.EMAIL],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.AUDIT_INSPECTION_WORKSPACE,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.EXPORT],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.REPORT_OF_FINDING,
        actions: [
          ActionEnum.VIEW,
          ActionEnum.EXPORT,
          ActionEnum.UPDATE,
          ActionEnum.EMAIL,
          ActionEnum.UPDATE_ROF,
        ],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.INTERNAL_AUDIT_REPORT,
        actions: [ActionEnum.VIEW, ActionEnum.EXPORT, ActionEnum.EXECUTE, ActionEnum.EMAIL],
      },

      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.INSPECTION_FOLLOW_UP,
        actions: [ActionEnum.VIEW, ActionEnum.EMAIL],
      },

      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.VESSEL,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.PORT_MASTER,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.AUDIT_CHECKLIST,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.REPORT_TEMPLATE,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.INSPECTION_MAPPING,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.FOCUS_REQUEST,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.MOBILE_CONFIG,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.INSPECTOR_TIME_OFF,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE],
      },
      // adding new permissions for voyage status
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.VOYAGE_STATUS,
        actions: [ActionEnum.VIEW],
      },
    ],
  },

  {
    name: ROLE_NAME_DEFAULT.INSPECTION.INTERNAL_INSPECTOR,
    description: ROLE_NAME_DEFAULT.INSPECTION.INTERNAL_INSPECTOR,
    permissions: [
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.DASHBOARD,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.MAP_VIEW,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.PLANNING_AND_REQUEST,
        actions: [ActionEnum.VIEW, ActionEnum.EXPORT, ActionEnum.EXECUTE, ActionEnum.EMAIL],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.AUDIT_INSPECTION_WORKSPACE,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.EXPORT],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.REPORT_OF_FINDING,
        actions: [ActionEnum.VIEW, ActionEnum.EXPORT, ActionEnum.UPDATE, ActionEnum.EMAIL],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.INTERNAL_AUDIT_REPORT,
        actions: [ActionEnum.VIEW, ActionEnum.EXPORT, ActionEnum.EXECUTE, ActionEnum.EMAIL],
      },

      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.INSPECTION_FOLLOW_UP,
        actions: [ActionEnum.VIEW, ActionEnum.EMAIL],
      },

      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.VESSEL,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.MOBILE_CONFIG,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.INSPECTOR_TIME_OFF,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE],
      },
      // adding new permissions for voyage status
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.VOYAGE_STATUS,
        actions: [ActionEnum.VIEW],
      },
    ],
  },
  {
    name: ROLE_NAME_DEFAULT.INSPECTION.EXTERNAL_INSPECTOR,
    description: ROLE_NAME_DEFAULT.INSPECTION.EXTERNAL_INSPECTOR,
    permissions: [
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.DASHBOARD,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.MAP_VIEW,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.PLANNING_AND_REQUEST,
        actions: [ActionEnum.VIEW, ActionEnum.EXPORT, ActionEnum.EXECUTE, ActionEnum.EMAIL],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.AUDIT_INSPECTION_WORKSPACE,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.EXPORT],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.REPORT_OF_FINDING,
        actions: [ActionEnum.VIEW, ActionEnum.EXPORT, ActionEnum.UPDATE, ActionEnum.EMAIL],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.INTERNAL_AUDIT_REPORT,
        actions: [ActionEnum.VIEW, ActionEnum.EXPORT, ActionEnum.EXECUTE, ActionEnum.EMAIL],
      },

      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.INSPECTION_FOLLOW_UP,
        actions: [ActionEnum.VIEW, ActionEnum.EMAIL],
      },

      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.MOBILE_CONFIG,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.INSPECTOR_TIME_OFF,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE],
      },
      // adding new permissions for voyage status
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.VOYAGE_STATUS,
        actions: [ActionEnum.VIEW],
      },
    ],
  },
  {
    name: ROLE_NAME_DEFAULT.INSPECTION.INSPECTION_REPORT_AUDIENCE,
    description: ROLE_NAME_DEFAULT.INSPECTION.INSPECTION_REPORT_AUDIENCE,
    permissions: [
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.DASHBOARD,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.MAP_VIEW,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.PLANNING_AND_REQUEST,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.AUDIT_INSPECTION_WORKSPACE,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.REPORT_OF_FINDING,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.INTERNAL_AUDIT_REPORT,
        actions: [ActionEnum.VIEW],
      },

      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.INSPECTION_FOLLOW_UP,
        actions: [ActionEnum.VIEW],
      },
      // adding new permissions for voyage status
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.VOYAGE_STATUS,
        actions: [ActionEnum.VIEW],
      },
    ],
  },
  {
    name: ROLE_NAME_DEFAULT.INSPECTION.INSPECTED_PARTY_AUDITEE,
    description: ROLE_NAME_DEFAULT.INSPECTION.INSPECTED_PARTY_AUDITEE,
    permissions: [
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.DASHBOARD,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.MAP_VIEW,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.PLANNING_AND_REQUEST,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.AUDIT_INSPECTION_WORKSPACE,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.REPORT_OF_FINDING,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.INTERNAL_AUDIT_REPORT,
        actions: [ActionEnum.VIEW, ActionEnum.EXPORT, ActionEnum.EXECUTE, ActionEnum.EMAIL],
      },

      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.INSPECTION_FOLLOW_UP,
        actions: [ActionEnum.VIEW, ActionEnum.EMAIL],
      },
      // adding new permissions for voyage status
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.VOYAGE_STATUS,
        actions: [ActionEnum.VIEW],
      },
    ],
  },
  //# end: region inspection

  //# start: region qa
  {
    name: ROLE_NAME_DEFAULT.QA.BPO_QA,
    description: ROLE_NAME_DEFAULT.QA.BPO_QA,
    permissions: [
      {
        feature: FeatureEnum.GROUP_COMPANY + '::' + SubFeatureEnum.COMPANY,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.USER_ROLE + '::' + SubFeatureEnum.ROLE_AND_PERMISSION,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.USER_ROLE + '::' + SubFeatureEnum.USER,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.AUDIT_TYPE,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.MAIN_CATEGORY,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.VESSEL,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.VIQ,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.VESSEL_TYPE,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.WORKFLOW_CONFIGURATION,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.PORT_MASTER,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.CARGO,
        actions: [ActionEnum.VIEW, ActionEnum.UPDATE, ActionEnum.CREATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.CARGO_TYPE,
        actions: [ActionEnum.VIEW, ActionEnum.UPDATE, ActionEnum.CREATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.CATEGORY_MAPPING,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.SECOND_CATEGORY,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.THIRD_CATEGORY,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.CHARTER_OWNER,
        actions: [ActionEnum.VIEW, ActionEnum.UPDATE, ActionEnum.CREATE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.AUTHORITY_MASTER,
        actions: [ActionEnum.VIEW, ActionEnum.UPDATE, ActionEnum.CREATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.INJURY_BODY,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.ELEMENT_MASTER,
        actions: [ActionEnum.VIEW, ActionEnum.UPDATE, ActionEnum.CREATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.EVENT_TYPE,
        actions: [ActionEnum.VIEW, ActionEnum.UPDATE, ActionEnum.CREATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.INCIDENT_MASTER,
        actions: [ActionEnum.VIEW, ActionEnum.UPDATE, ActionEnum.CREATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.INJURY_MASTER,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.TECH_ISSUE_NOTE,
        actions: [ActionEnum.VIEW, ActionEnum.UPDATE, ActionEnum.CREATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.PLANS_DRAWINGS_MASTER,
        actions: [ActionEnum.VIEW, ActionEnum.UPDATE, ActionEnum.CREATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.PSC_ACTION,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.PSC_DEFICIENCY,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.STANDARD_MASTER,
        actions: [ActionEnum.VIEW, ActionEnum.UPDATE, ActionEnum.CREATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.TERMINAL,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.TRANSFER_TYPE,
        actions: [ActionEnum.VIEW, ActionEnum.UPDATE, ActionEnum.CREATE, ActionEnum.DELETE],
      },
      // adding new permissions for voyage status
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.VOYAGE_STATUS,
        actions: [ActionEnum.VIEW],
      },
    ],
  },
  {
    name: ROLE_NAME_DEFAULT.QA.VETTING_SUPERINTENDANT,
    description: ROLE_NAME_DEFAULT.QA.VETTING_SUPERINTENDANT,
    permissions: [
      {
        feature: FeatureEnum.GROUP_COMPANY + '::' + SubFeatureEnum.COMPANY,
        actions: [ActionEnum.VIEW],
      },
      {
        feature:
          FeatureEnum.QUALITY_ASSURANCE_SELF_ASSESSMENT + '::' + SubFeatureEnum.SELF_ASSESSMENT,
        actions: [ActionEnum.VIEW, ActionEnum.UPDATE],
      },
      {
        feature:
          FeatureEnum.QUALITY_ASSURANCE_SAILING_REPORT +
          '::' +
          SubFeatureEnum.SAILING_GENERAL_REPORT,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE],
      },
      {
        feature: FeatureEnum.QUALITY_ASSURANCE_INCIDENTS + '::' + SubFeatureEnum.SUMMARY,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.QUALITY_ASSURANCE_INCIDENTS + '::' + SubFeatureEnum.INCIDENTS,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.RESTRICTED],
      },
      {
        feature:
          FeatureEnum.QUALITY_ASSURANCE_PILOT_TERMINAL_FEEDBACK +
          '::' +
          SubFeatureEnum.PILOT_TERMINAL_FEEDBACK,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.RESTRICTED],
      },
      {
        feature:
          FeatureEnum.QUALITY_ASSURANCE_VESSEL_SCREENING + '::' + SubFeatureEnum.VESSEL_SCREENING,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.PORT_MASTER,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.TERMINAL,
        actions: [ActionEnum.VIEW],
      },
      // adding new permissions for voyage status
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.VOYAGE_STATUS,
        actions: [ActionEnum.VIEW],
      },
    ],
  },
  {
    name: ROLE_NAME_DEFAULT.QA.VETTING_MANAGER,
    description: ROLE_NAME_DEFAULT.QA.VETTING_MANAGER,
    permissions: [
      {
        feature: FeatureEnum.GROUP_COMPANY + '::' + SubFeatureEnum.COMPANY,
        actions: [ActionEnum.VIEW],
      },
      {
        feature:
          FeatureEnum.QUALITY_ASSURANCE_SELF_ASSESSMENT + '::' + SubFeatureEnum.SELF_ASSESSMENT,
        actions: [ActionEnum.VIEW, ActionEnum.UPDATE, ActionEnum.REVIEW],
      },
      {
        feature:
          FeatureEnum.QUALITY_ASSURANCE_SAILING_REPORT +
          '::' +
          SubFeatureEnum.SAILING_GENERAL_REPORT,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE],
      },
      {
        feature: FeatureEnum.QUALITY_ASSURANCE_INCIDENTS + '::' + SubFeatureEnum.SUMMARY,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.QUALITY_ASSURANCE_INCIDENTS + '::' + SubFeatureEnum.INCIDENTS,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE],
      },
      {
        feature:
          FeatureEnum.QUALITY_ASSURANCE_PILOT_TERMINAL_FEEDBACK +
          '::' +
          SubFeatureEnum.PILOT_TERMINAL_FEEDBACK,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.RESTRICTED],
      },
      {
        feature:
          FeatureEnum.QUALITY_ASSURANCE_VESSEL_SCREENING + '::' + SubFeatureEnum.VESSEL_SCREENING,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.PORT_MASTER,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.TERMINAL,
        actions: [ActionEnum.VIEW],
      },
      // adding new permissions for voyage status
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.VOYAGE_STATUS,
        actions: [ActionEnum.CREATE, ActionEnum.VIEW, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
    ],
  },
  {
    name: ROLE_NAME_DEFAULT.QA.COMMERCIAL_TEAM,
    description: ROLE_NAME_DEFAULT.QA.COMMERCIAL_TEAM,
    permissions: [
      {
        feature: FeatureEnum.QUALITY_ASSURANCE_INCIDENTS + '::' + SubFeatureEnum.SUMMARY,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.QUALITY_ASSURANCE_INCIDENTS + '::' + SubFeatureEnum.INCIDENTS,
        actions: [ActionEnum.VIEW],
      },
      {
        feature:
          FeatureEnum.QUALITY_ASSURANCE_PILOT_TERMINAL_FEEDBACK +
          '::' +
          SubFeatureEnum.PILOT_TERMINAL_FEEDBACK,
        actions: [ActionEnum.VIEW],
      },
      {
        feature:
          FeatureEnum.QUALITY_ASSURANCE_VESSEL_SCREENING + '::' + SubFeatureEnum.VESSEL_SCREENING,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE],
      },
      // adding new permissions for voyage status
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.VOYAGE_STATUS,
        actions: [ActionEnum.VIEW],
      },
    ],
  },
  {
    name: ROLE_NAME_DEFAULT.QA.OPERATOR_DOC_HOLDER,
    description: ROLE_NAME_DEFAULT.QA.OPERATOR_DOC_HOLDER,
    permissions: [
      {
        feature:
          FeatureEnum.QUALITY_ASSURANCE_SELF_ASSESSMENT + '::' + SubFeatureEnum.SELF_ASSESSMENT,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE],
      },
      {
        feature:
          FeatureEnum.QUALITY_ASSURANCE_SAILING_REPORT +
          '::' +
          SubFeatureEnum.SAILING_GENERAL_REPORT,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE],
      },
      {
        feature: FeatureEnum.QUALITY_ASSURANCE_INCIDENTS + '::' + SubFeatureEnum.SUMMARY,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.QUALITY_ASSURANCE_INCIDENTS + '::' + SubFeatureEnum.INCIDENTS,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.RESTRICTED],
      },
      // adding new permissions for voyage status
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.VOYAGE_STATUS,
        actions: [ActionEnum.VIEW],
      },
    ],
  },
  {
    name: ROLE_NAME_DEFAULT.QA.PILOT_TERMINAL,
    description: ROLE_NAME_DEFAULT.QA.PILOT_TERMINAL,
    permissions: [
      {
        feature:
          FeatureEnum.QUALITY_ASSURANCE_PILOT_TERMINAL_FEEDBACK +
          '::' +
          SubFeatureEnum.PILOT_TERMINAL_FEEDBACK,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.RESTRICTED],
      },
    ],
  },

  //# end: region qa
];
