export class AppConst {
  static SCHEMA_OPTIONS: any = {
    versionKey: false,
    toJSON: {
      virtuals: true,
    },
    toObject: {
      virtuals: true,
    },
    id: false,
    timestamps: {
      createdAt: 'dateCreated',
      updatedAt: 'dateUpdated',
    },
  };

  static readonly BOOLEAN: any = {
    TRUE: 'true',
    FALSE: 'false',
  };

  static readonly API_PREFIX: string = 'api';

  static readonly API_VERSION: string = 'v1';

  static readonly PAGE_SIZE: number = 20;

  // #endregion
  static readonly CODE_FORGOT = {
    TIME_FORGOT_EXPIRE: 2, // 2 days,
  };

  static readonly CODE_REGISTER = {
    TIME_REGISTER_EXPIRE: 2, // 2 days
  };
}
