import { ROLE_NAME_DEFAULT } from '../../modules/iam/config';
import { ActionEnum, FeatureEnum, SubFeatureEnum } from '../enums';

export const DEFAULT_ROLES_QA = [
  {
    name: ROLE_NAME_DEFAULT.COMMON.COMPANY_LOCAL_ADMIN,
    description: ROLE_NAME_DEFAULT.COMMON.COMPANY_LOCAL_ADMIN,
    permissions: [
      {
        feature: FeatureEnum.GROUP_COMPANY + '::' + SubFeatureEnum.COMPANY,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.USER_ROLE + '::' + SubFeatureEnum.ROLE_AND_PERMISSION,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.USER_ROLE + '::' + SubFeatureEnum.USER,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.DIVISION,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.DEPARTMENT_MASTER,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },

      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.VESSEL,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.VIQ,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.VESSEL_TYPE,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.WORKFLOW_CONFIGURATION,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      // {
      //   feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.CHARTER_OWNER,
      //   actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      // },
      // {
      //   feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.DEVICE_CONTROL,
      //   actions: [ActionEnum.VIEW],
      // },
      // {
      //   feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.MAIL_TEMPLATE,
      //   actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      // },
      // {
      //   feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.MOBILE_CONFIG,
      //   actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      // },
      // {
      //   feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.ANSWER_VALUE,
      //   actions: [ActionEnum.VIEW],
      // },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.AUTHORITY_MASTER,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.STANDARD_MASTER,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.ELEMENT_MASTER,
        actions: [ActionEnum.VIEW],
      },
    ],
  },
  {
    name: ROLE_NAME_DEFAULT.QA.BPO_QA,
    description: ROLE_NAME_DEFAULT.QA.BPO_QA,
    permissions: [
      {
        feature: FeatureEnum.GROUP_COMPANY + '::' + SubFeatureEnum.COMPANY,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.USER_ROLE + '::' + SubFeatureEnum.ROLE_AND_PERMISSION,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.USER_ROLE + '::' + SubFeatureEnum.USER,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.AUDIT_TYPE,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.MAIN_CATEGORY,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.VESSEL,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.VIQ,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.VESSEL_TYPE,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.WORKFLOW_CONFIGURATION,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.PORT_MASTER,
        actions: [ActionEnum.VIEW],
      },
      // {
      //   feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.CATEGORY_MAPPING,
      //   actions: [ActionEnum.VIEW],
      // },
      // {
      //   feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.SECOND_CATEGORY,
      //   actions: [ActionEnum.VIEW],
      // },
      // {
      //   feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.THIRD_CATEGORY,
      //   actions: [ActionEnum.VIEW],
      // },
      // {
      //   feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.CHARTER_OWNER,
      //   actions: [ActionEnum.VIEW, ActionEnum.UPDATE, ActionEnum.CREATE],
      // },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.STANDARD_MASTER,
        actions: [ActionEnum.VIEW, ActionEnum.UPDATE, ActionEnum.CREATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.ELEMENT_MASTER,
        actions: [ActionEnum.VIEW, ActionEnum.UPDATE, ActionEnum.CREATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.AUTHORITY_MASTER,
        actions: [ActionEnum.VIEW, ActionEnum.UPDATE, ActionEnum.CREATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.INJURY_BODY,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.CARGO,
        actions: [ActionEnum.VIEW, ActionEnum.UPDATE, ActionEnum.CREATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.CARGO_TYPE,
        actions: [ActionEnum.VIEW, ActionEnum.UPDATE, ActionEnum.CREATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.EVENT_TYPE,
        actions: [ActionEnum.VIEW, ActionEnum.UPDATE, ActionEnum.CREATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.INCIDENT_MASTER,
        actions: [ActionEnum.VIEW, ActionEnum.UPDATE, ActionEnum.CREATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.INJURY_MASTER,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.TECH_ISSUE_NOTE,
        actions: [ActionEnum.VIEW, ActionEnum.UPDATE, ActionEnum.CREATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.PLANS_DRAWINGS_MASTER,
        actions: [ActionEnum.VIEW, ActionEnum.UPDATE, ActionEnum.CREATE, ActionEnum.DELETE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.PSC_ACTION,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.PSC_DEFICIENCY,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.TERMINAL,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.TRANSFER_TYPE,
        actions: [ActionEnum.VIEW, ActionEnum.UPDATE, ActionEnum.CREATE, ActionEnum.DELETE],
      },
    ],
  },
  {
    name: ROLE_NAME_DEFAULT.QA.VETTING_SUPERINTENDANT,
    description: ROLE_NAME_DEFAULT.QA.VETTING_SUPERINTENDANT,
    permissions: [
      {
        feature: FeatureEnum.GROUP_COMPANY + '::' + SubFeatureEnum.COMPANY,
        actions: [ActionEnum.VIEW],
      },
      {
        feature:
          FeatureEnum.QUALITY_ASSURANCE_SELF_ASSESSMENT + '::' + SubFeatureEnum.SELF_ASSESSMENT,
        actions: [ActionEnum.VIEW, ActionEnum.UPDATE],
      },
      {
        feature:
          FeatureEnum.QUALITY_ASSURANCE_SAILING_REPORT +
          '::' +
          SubFeatureEnum.SAILING_GENERAL_REPORT,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE],
      },
      {
        feature: FeatureEnum.QUALITY_ASSURANCE_INCIDENTS + '::' + SubFeatureEnum.SUMMARY,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.QUALITY_ASSURANCE_INCIDENTS + '::' + SubFeatureEnum.INCIDENTS,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.RESTRICTED],
      },
      {
        feature:
          FeatureEnum.QUALITY_ASSURANCE_PILOT_TERMINAL_FEEDBACK +
          '::' +
          SubFeatureEnum.PILOT_TERMINAL_FEEDBACK,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.RESTRICTED],
      },
      {
        feature:
          FeatureEnum.QUALITY_ASSURANCE_VESSEL_SCREENING + '::' + SubFeatureEnum.VESSEL_SCREENING,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.PORT_MASTER,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.TERMINAL,
        actions: [ActionEnum.VIEW],
      },
    ],
  },
  {
    name: ROLE_NAME_DEFAULT.QA.VETTING_MANAGER,
    description: ROLE_NAME_DEFAULT.QA.VETTING_MANAGER,
    permissions: [
      {
        feature: FeatureEnum.GROUP_COMPANY + '::' + SubFeatureEnum.COMPANY,
        actions: [ActionEnum.VIEW],
      },
      {
        feature:
          FeatureEnum.QUALITY_ASSURANCE_SELF_ASSESSMENT + '::' + SubFeatureEnum.SELF_ASSESSMENT,
        actions: [ActionEnum.VIEW, ActionEnum.UPDATE],
      },
      {
        feature:
          FeatureEnum.QUALITY_ASSURANCE_SAILING_REPORT +
          '::' +
          SubFeatureEnum.SAILING_GENERAL_REPORT,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE],
      },
      {
        feature: FeatureEnum.QUALITY_ASSURANCE_INCIDENTS + '::' + SubFeatureEnum.SUMMARY,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.QUALITY_ASSURANCE_INCIDENTS + '::' + SubFeatureEnum.INCIDENTS,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE],
      },
      {
        feature:
          FeatureEnum.QUALITY_ASSURANCE_PILOT_TERMINAL_FEEDBACK +
          '::' +
          SubFeatureEnum.PILOT_TERMINAL_FEEDBACK,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.RESTRICTED],
      },
      {
        feature:
          FeatureEnum.QUALITY_ASSURANCE_VESSEL_SCREENING + '::' + SubFeatureEnum.VESSEL_SCREENING,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE],
      },
      {
        feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.PORT_MASTER,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.TERMINAL,
        actions: [ActionEnum.VIEW],
      },
    ],
  },
  {
    name: ROLE_NAME_DEFAULT.QA.COMMERCIAL_TEAM,
    description: ROLE_NAME_DEFAULT.QA.COMMERCIAL_TEAM,
    permissions: [
      {
        feature: FeatureEnum.QUALITY_ASSURANCE_INCIDENTS + '::' + SubFeatureEnum.SUMMARY,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.QUALITY_ASSURANCE_INCIDENTS + '::' + SubFeatureEnum.INCIDENTS,
        actions: [ActionEnum.VIEW],
      },
      {
        feature:
          FeatureEnum.QUALITY_ASSURANCE_PILOT_TERMINAL_FEEDBACK +
          '::' +
          SubFeatureEnum.PILOT_TERMINAL_FEEDBACK,
        actions: [ActionEnum.VIEW],
      },
      {
        feature:
          FeatureEnum.QUALITY_ASSURANCE_VESSEL_SCREENING + '::' + SubFeatureEnum.VESSEL_SCREENING,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE],
      },
    ],
  },
  {
    name: ROLE_NAME_DEFAULT.QA.OPERATOR_DOC_HOLDER,
    description: ROLE_NAME_DEFAULT.QA.OPERATOR_DOC_HOLDER,
    permissions: [
      {
        feature:
          FeatureEnum.QUALITY_ASSURANCE_SELF_ASSESSMENT + '::' + SubFeatureEnum.SELF_ASSESSMENT,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE],
      },
      {
        feature:
          FeatureEnum.QUALITY_ASSURANCE_SAILING_REPORT +
          '::' +
          SubFeatureEnum.SAILING_GENERAL_REPORT,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE],
      },
      {
        feature: FeatureEnum.QUALITY_ASSURANCE_INCIDENTS + '::' + SubFeatureEnum.SUMMARY,
        actions: [ActionEnum.VIEW],
      },
      {
        feature: FeatureEnum.QUALITY_ASSURANCE_INCIDENTS + '::' + SubFeatureEnum.INCIDENTS,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.RESTRICTED],
      },
      {
        feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.INSPECTION_FOLLOW_UP,
        actions: [ActionEnum.UPDATE_CAR],
      },
    ],
  },
  {
    name: ROLE_NAME_DEFAULT.QA.PILOT_TERMINAL,
    description: ROLE_NAME_DEFAULT.QA.PILOT_TERMINAL,
    permissions: [
      {
        feature:
          FeatureEnum.QUALITY_ASSURANCE_PILOT_TERMINAL_FEEDBACK +
          '::' +
          SubFeatureEnum.PILOT_TERMINAL_FEEDBACK,
        actions: [ActionEnum.VIEW, ActionEnum.CREATE, ActionEnum.UPDATE, ActionEnum.RESTRICTED],
      },
    ],
  },
];
