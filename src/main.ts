global.__CONFIGMAP_PATH = '/etc/config';

if (['local', 'test'].indexOf(process.env.NODE_ENV) === -1) {
  require('elastic-apm-node').start({
    serviceName: `svm-iam`,
    environment: process.env.NODE_ENV,
  });
}
import { NestFactory } from '@nestjs/core';
import { BadRequestException, HttpStatus, ValidationPipe } from '@nestjs/common';
import * as signale from 'signale';
import * as compression from 'compression';
import * as helmet from 'helmet';
import * as morgan from 'morgan';

import APP_CONFIG from './configs/app.config'; // eager loading
import { initConfigLib } from './configs/lib.config';
import bootstrapConfig from './configs/bootstrap.config';
import initSwagger from './configs/swagger.config';

import { AppConst } from './commons/consts/app.const';
import { AppModule } from './modules/app/app.module';

async function bootstrap() {
  initConfigLib();
  bootstrapConfig();

  const server = await NestFactory.create(AppModule);
  server.enableCors({
    origin: APP_CONFIG.ENV.SHARE.SECURE.CORS.ORIGIN,
    methods: [],
    allowedHeaders: APP_CONFIG.ENV.SHARE.SECURE.CORS.ALLOWED_HEADERS,
    exposedHeaders: APP_CONFIG.ENV.SHARE.SECURE.CORS.EXPOSED_HEADERS,
    credentials: APP_CONFIG.ENV.SHARE.SECURE.CORS.CREDENTIALS,
    preflightContinue: APP_CONFIG.ENV.SHARE.SECURE.CORS.PREFLIGHT_CONTINUE,
  });

  server.setGlobalPrefix(`/${AppConst.API_PREFIX}/${AppConst.API_VERSION}`);

  server.use(helmet());
  server.use(compression());
  server.use(morgan('dev'));

  initSwagger(server);

  // Auto validation DTO at application level
  server.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      errorHttpStatusCode: HttpStatus.BAD_REQUEST,
      transform: true, // to enable class-transform
      // dismissDefaultMessages: true,
      exceptionFactory: (errors) => new BadRequestException(errors),
    }),
  );

  // Start server
  await server.listen(APP_CONFIG.ENV.APP.PORT, () => {
    // const serverAddress: any = server.address
    signale.success(`Server's running at port ${APP_CONFIG.ENV.APP.PORT}`);
  });
}
bootstrap();
