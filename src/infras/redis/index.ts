import { Global, Module } from '@nestjs/common';
import { RedisModule } from 'nestjs-redis';
import APP_CONFIG from '../../configs/app.config';
import { TokenCacheService } from './token-cache.service';

@Global()
@Module({
  imports: [
    RedisModule.register({
      host: APP_CONFIG.ENV.DATABASE.REDIS.HOST,
      db: APP_CONFIG.ENV.DATABASE.REDIS.DB, // usually is 0
      password: APP_CONFIG.ENV.DATABASE.REDIS.PASSWORD,
      port: APP_CONFIG.ENV.DATABASE.REDIS.PORT,
      keyPrefix: APP_CONFIG.ENV.DATABASE.REDIS.KEY_PREFIX,
      enableReadyCheck: true,
      onClientReady: (client) => {
        client.on('ready', () => console.info('Connected to Redis...'));
      },
    }),
  ],
  providers: [TokenCacheService],
  exports: [TokenCacheService],
})
export class MyRedisModule {}
