import { Redis } from 'ioredis';

export class MyRedisBase {
  protected redisClient: Redis;
  constructor(redisClient: Redis) {
    this.redisClient = redisClient;
  }

  protected async _deleteKeysByPattern(pattern: string) {
    return new Promise((resolve, reject) => {
      const stream = this.redisClient.scanStream({
        match: pattern,
      });
      stream.on('data', (keys) => {
        if (keys.length) {
          const pipeline = this.redisClient.pipeline();
          keys.forEach((key) => {
            pipeline.unlink(key);
          });
          pipeline.exec();
        }
      });

      stream.on('end', () => {
        resolve(1);
      });
      stream.on('error', (err) => {
        console.error('[MyRedisBase] _deleteKeysByPattern error', err);
        reject(err);
      });
    });
  }
}
