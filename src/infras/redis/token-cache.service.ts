import { Injectable } from '@nestjs/common';
import { RedisService } from 'nestjs-redis';
import { RoleScopeCheck, TokenPayloadModel, Utils } from 'svm-nest-lib';
import { ITokenDeletingModel } from '../../modules/iam/consumer/token-deleting.consumer';
import { MyRedisBase } from './my-redis-base';

@Injectable()
export class TokenCacheService extends MyRedisBase {
  private readonly TOKEN_HASH_KEY = 'tokens';

  constructor(redisService: RedisService) {
    super(redisService.getClient());
  }

  private _genTokenKey(user: TokenPayloadModel, hashToken: string) {
    let key = this.TOKEN_HASH_KEY + ':';
    // if (RoleScopeCheck.isSuperAdmin(user)) {
    //   console.log("RoleScopeCheck.isSuperAdmin(user) ",RoleScopeCheck.isSuperAdmin(user))
    //   key += `${user.id}`;
    // } else if (RoleScopeCheck.isAdminOfParent(user) || RoleScopeCheck.isUserOfParent(user)) {
    // console.log("RoleScopeCheck.isAdminOfParent(user) ",RoleScopeCheck.isAdminOfParent(user))
    // console.log("RoleScopeCheck.isUserOfParent(user) ",RoleScopeCheck.isUserOfParent(user))
    //   key += `${user.companyId}:${user.id}`;
    // } else if (RoleScopeCheck.isAdminOfSub(user) || RoleScopeCheck.isUserOfSub(user)) {
    // console.log("RoleScopeCheck.isAdminOfSub(user) ",RoleScopeCheck.isAdminOfSub(user))
    // console.log("RoleScopeCheck.isUserOfSub(user) ",RoleScopeCheck.isUserOfSub(user))
    //   key += `${user.parentCompanyId}:${user.companyId}:${user.id}`;
    // } else {
    //   key += `${user.id}`;
    // }
    if (user.parentCompanyId) {
      key += `${user.parentCompanyId}:${user.companyId}:${user.id}`;
    } else if (user.companyId) {
      key += `${user.companyId}:${user.id}`;
    } else {
      key += `${user.id}`;
    }

    return key + ':' + hashToken;
  }

  public async saveTokenToCache(user: TokenPayloadModel, token: string) {
    const hashToken = Utils.strings.hashString(token, '', 'sha256');
    const key = this._genTokenKey(user, hashToken);
    const expireTimeDuration = user.exp - Math.floor(Date.now() / 1000);
    return this.redisClient.set(key, 1, 'ex', expireTimeDuration);
  }

  public async checkTokenInCache(user: TokenPayloadModel, token: string) {
    const hashToken = Utils.strings.hashString(token, '', 'sha256');
    const key = this._genTokenKey(user, hashToken);
    const rs = await this.redisClient.exists(key);
    return rs;
  }

  public async deleteTokenKeys(param: ITokenDeletingModel) {
    // Check if company is disable (inactive or deleted)
    let tokenKeyPattern = this.TOKEN_HASH_KEY + ':';
    if (param.entity === 'company') {
      // Check if is parent company
      if (!param.companyId) {
        tokenKeyPattern += `${param.userId}:`;
      } else if (!param.parentCompanyId) {
        tokenKeyPattern += `${param.companyId}:${param.userId}:`;
      } else {
        tokenKeyPattern += `${param.parentCompanyId}:${param.companyId}:`;
      }
    } else if (param.entity === 'user') {
      // Check if is user/admin of parent company
      if (!param.companyId) {
        tokenKeyPattern += `${param.userId}:`;
      } else if (!param.parentCompanyId) {
        tokenKeyPattern += `${param.companyId}:${param.userId}:`;
      } else {
        tokenKeyPattern += `${param.parentCompanyId}:${param.companyId}:${param.userId}:`;
      }
    }

    tokenKeyPattern += '*';
    console.log('tokenKeyPattern ', tokenKeyPattern);
    const deleted = this._deleteKeysByPattern(tokenKeyPattern);
    if (deleted) return 'Token was Deleted...,';
    return 'Token was not Deleted...!';
  }

  public async deleteTokenSpecific(
    param: ITokenDeletingModel,
    user: TokenPayloadModel,
    token: string,
  ) {
    // Check if company is disable (inactive or deleted)
    let tokenKeyPattern = this.TOKEN_HASH_KEY + ':';
    if (param.entity === 'company') {
      // Check if is parent company
      if (!param.companyId) {
        tokenKeyPattern += `${param.userId}:`;
      } else if (!param.parentCompanyId) {
        tokenKeyPattern += `${param.companyId}:${param.userId}:`;
      } else {
        tokenKeyPattern += `${param.parentCompanyId}:${param.companyId}:`;
      }
    } else if (param.entity === 'user') {
      // Check if is user/admin of parent company
      if (!param.companyId) {
        tokenKeyPattern += `${param.userId}:`;
      } else if (!param.parentCompanyId) {
        tokenKeyPattern += `${param.companyId}:${param.userId}:`;
      } else {
        tokenKeyPattern += `${param.parentCompanyId}:${param.companyId}:${param.userId}:`;
      }
    }
    const hashToken = Utils.strings.hashString(token, '', 'sha256');

    tokenKeyPattern += `${hashToken}`;
    console.log('tokenKeyPattern ', tokenKeyPattern);
    const deleted = this._deleteKeysByPattern(tokenKeyPattern);
    if (deleted) return 'Token was Deleted...,';
    return 'Token was not Deleted...!';
  }
}
