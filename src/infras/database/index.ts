import { join } from 'path';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Environment } from 'svm-nest-lib';
import APP_CONFIG from '../../configs/app.config';

@Module({
  imports: [
    TypeOrmModule.forRoot({
      type: 'postgres',
      host: APP_CONFIG.ENV.DATABASE.POSTGRES.HOST,
      port: APP_CONFIG.ENV.DATABASE.POSTGRES.PORT,
      username: APP_CONFIG.ENV.DATABASE.POSTGRES.USERNAME,
      password: APP_CONFIG.ENV.DATABASE.POSTGRES.PASSWORD,
      database: APP_CONFIG.ENV.DATABASE.POSTGRES.NAME,
      autoLoadEntities: true,
      connectTimeoutMS: 0,
      logNotifications: true,
      synchronize: false,
      entities: [join(__dirname, '../../modules/**/*.entity{.ts,.js}')],
      migrations: [join(__dirname, '../../migrations/*{.ts,.js}')],
      migrationsRun: true,
      logging: [Environment.LOCAL, Environment.DEV].includes(process.env.NODE_ENV as Environment)
        ? 'all'
        : ['warn', 'error'],
      poolErrorHandler: (err) => {
        console.error('[INautix] Connection pool error', err);
      },
      extra: {
        connectionTimeoutMillis: 30000, // 30s
        idleTimeoutMillis: 10000, // 10s
        max: 25, // 25 connections in pool
      },
    }),
  ],
})
export class DatabaseModule {}
