import { Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bull';
import APP_CONFIG from '../../configs/app.config';

@Module({
  imports: [
    BullModule.forRoot({
      redis: {
        host: APP_CONFIG.ENV.DATABASE.REDIS.HOST,
        db: APP_CONFIG.ENV.DATABASE.REDIS.DB + 1, // job queue reside on logical main DB + 1
        password: APP_CONFIG.ENV.DATABASE.REDIS.PASSWORD,
        port: APP_CONFIG.ENV.DATABASE.REDIS.PORT,
      },
    }),
  ],
})
export class MessageBrokerModule {}
