import { Injectable } from '@nestjs/common';
import { Queue } from 'bull';
import { InjectQueue } from '@nestjs/bull';
import { QUEUE_NAME } from './queue-name.const';
import { BaseProducer } from './base.producer';

export interface IEmailEventModel {
  to: string[] | string;
  subject: string;
  bcc?: string[];
  cc?: string[];
  templateKey: string;
  data: any;
  text?: string;
  lang?: string;
}

@Injectable()
export class EmailProducer extends BaseProducer<IEmailEventModel> {
  constructor(@InjectQueue(QUEUE_NAME.EMAILS) emailQueue: Queue) {
    super([emailQueue]);
  }

  async publishEmail(data: IEmailEventModel) {
    // Ref jobOptions: https://docs.nestjs.com/techniques/queues#job-options
    const job = await this.publish(data);
    return job;
  }
}
