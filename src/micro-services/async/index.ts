import { BullModule } from '@nestjs/bull';
import { Modu<PERSON> } from '@nestjs/common';
import { EmailProducer } from './email.producer';
import { QUEUE_NAME } from './queue-name.const';
// import { RoleProducer } from './roles.producer';

@Module({
  imports: [
    BullModule.registerQueue(
      {
        name: QUEUE_NAME.EMAILS,
      },
      {
        name: QUEUE_NAME.TOKEN_DELETING,
      },
      {
        name: QUEUE_NAME.COMPANIES_CHANGE_SUB1,
      },
    ),
  ],
  providers: [EmailProducer],
  exports: [EmailProducer],
})
export class MicroservicesAsyncModule {}
