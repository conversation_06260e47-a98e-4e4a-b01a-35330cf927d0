import { Queue } from 'bull';
import { Utils } from 'svm-nest-lib';

export interface IJobOptions {
  delay?: number;
}

export class BaseProducer<T> {
  private queues: Queue<T>[];

  constructor(queues: Queue[]) {
    this.queues = queues;
  }

  protected async publish(data: T, options?: IJobOptions) {
    return Promise.all(
      this.queues.map((q) =>
        q.add(data, {
          removeOnComplete: 100,
          removeOnFail: 1000,
          jobId: Utils.strings.generateUniqueString(),
          ...options,
        }),
      ),
    );
  }
}
