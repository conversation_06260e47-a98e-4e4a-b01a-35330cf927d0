import { Injectable } from '@nestjs/common';
import { Queue } from 'bull';
import { InjectQueue } from '@nestjs/bull';
import { QUEUE_NAME } from './queue-name.const';
import { BaseProducer } from './base.producer';
import { DataChangeEvent } from '../../commons/enums';

export interface IRoleEventModel {
  operation: DataChangeEvent;
  roleData: {
    id: string;
    name: string;
  };
}

@Injectable()
export class RoleProducer extends BaseProducer<IRoleEventModel> {
  constructor(@InjectQueue(QUEUE_NAME.ROLES_CHANGE_SUB1) rolesChangeQueue: Queue) {
    super([rolesChangeQueue]);
  }

  async publishRoleChange(data: IRoleEventModel) {
    // Ref jobOptions: https://docs.nestjs.com/techniques/queues#job-options
    // Should use _.publish method to send event to queue
    const job = await this.publish(data);
    return job;
  }
}
