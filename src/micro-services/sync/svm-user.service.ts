import { Injectable } from '@nestjs/common';
import BaseHttp from './base-http';
import { BaseError, Request, ServiceEnum } from 'svm-nest-lib';
import { UserModel } from './model/user.model';

@Injectable()
export class SVMUserService extends BaseHttp {
  async getUsersByIds(
    userIds: string[],
    vesselId?: string,
    companyId?: string,
    companyLevel?: string,
    companyIds?: string[],
  ) {
    try {
      // console.log(userIds, vesselId, companyId);
      const { data } = await Request.post<UserModel[]>(`internal/user/list-by-ids-vessel-id`, {
        body: {
          userIds,
          vesselId,
          companyId,
          companyLevel,
          companyIds,
        },
        serviceName: ServiceEnum.ASSETS,
      });
      return data;
    } catch (ex) {
      if (ex.response?.data) {
        throw new BaseError({
          message: ex.response.data.message,
          status: ex.response.data.statusCode,
        });
      }
      throw ex;
    }
  }

  async getUsernamesByIds(
    userIds: string[],
    vesselId?: string,
    companyId?: string,
    companyLevel?: string,
  ) {
    try {
      const { data } = await Request.post<UserModel[]>(`internal/user/list-by-ids`, {
        body: {
          userIds,
          vesselId,
          companyId,
          companyLevel,
        },
        serviceName: ServiceEnum.ASSETS,
      });
      return data;
    } catch (ex) {
      if (ex.response?.data) {
        throw new BaseError({
          message: ex.response.data.message,
          status: ex.response.data.statusCode,
        });
      }
      throw ex;
    }
  }
}
