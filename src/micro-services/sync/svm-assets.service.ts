import { Injectable } from '@nestjs/common';
import BaseHttp from './base-http';
import { BaseError, Request, ServiceEnum } from 'svm-nest-lib';
import { CompanyModel } from './model/assets.model';

@Injectable()
export class SVMAssetsService extends BaseHttp {
  async listAllCompanies() {
    try {
      const { data } = await Request.get<CompanyModel[]>(`internal/company`, {
        serviceName: ServiceEnum.ASSETS,
      });
      return data;
    } catch (ex) {
      if (ex.response?.data) {
        throw new BaseError({
          message: ex.response.data.message,
          status: ex.response.data.statusCode,
        });
      }
      throw ex;
    }
  }

  async listAllChildCompanies(parentCompanyId: string) {
    try {
      const { data } = await Request.get<CompanyModel[]>(
        `internal/company/${parentCompanyId}/children`,
        {
          serviceName: ServiceEnum.ASSETS,
        },
      );
      return data;
    } catch (ex) {
      if (ex.response?.data) {
        throw new BaseError({
          message: ex.response.data.message,
          status: ex.response.data.statusCode,
        });
      }
      throw ex;
    }
  }

  async getDetailCompany(companyId: string) {
    try {
      const { data } = await Request.get<CompanyModel>(`internal/company/${companyId}`, {
        serviceName: ServiceEnum.ASSETS,
      });
      return data;
    } catch (ex) {
      if (ex.response?.data) {
        throw new BaseError({
          message: ex.response.data.message,
          status: ex.response.data.statusCode,
        });
      }
      throw ex;
    }
  }
}
