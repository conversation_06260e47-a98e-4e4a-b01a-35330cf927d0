import { MigrationInterface, QueryRunner } from 'typeorm';

export class addNewFieldForRole1680163067517 implements MigrationInterface {
  name = 'addNewFieldForRole1680163067517';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "public"."role" ADD "isInspection" boolean`);
    await queryRunner.query(`ALTER TABLE "public"."role" ADD "isQA" boolean`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
