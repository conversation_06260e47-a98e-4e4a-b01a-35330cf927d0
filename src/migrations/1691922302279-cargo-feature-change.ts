import { MigrationInterface, QueryRunner } from 'typeorm';

export class cargoFeatureChange1691922302279 implements MigrationInterface {
  name = 'cargoFeatureChange1691922302279';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `UPDATE "public"."feature" SET "name" = 'Configuration::Common::Cargo', "parentId" = (SELECT "id" FROM "public"."feature" WHERE "name" = 'Configuration::Common') WHERE "name" = 'Configuration::QA::Cargo'`,
    );
    await queryRunner.query(
      `UPDATE "public"."feature" SET "name" = 'Configuration::Common::Cargo Type', "parentId" = (SELECT "id" FROM "public"."feature" WHERE "name" = 'Configuration::Common') WHERE "name" = 'Configuration::QA::Cargo Type'`,
    );
    await queryRunner.query(
      `UPDATE "public"."user_role_permission" SET "featureName" = 'Configuration::Common::Cargo' WHERE "featureName" = 'Configuration::QA::Cargo'`,
    );
    await queryRunner.query(
      `UPDATE "public"."user_role_permission" SET "featureName" = 'Configuration::Common::Cargo Type' WHERE "featureName" = 'Configuration::QA::Cargo Type'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
