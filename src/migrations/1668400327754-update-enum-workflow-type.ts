import { MigrationInterface, QueryRunner } from 'typeorm';

export class updateEnumWorkflowType1668400327754 implements MigrationInterface {
  name = 'updateEnumWorkflowType1668400327754';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TYPE "public"."workflow_workflowtype_enum" RENAME TO "workflow_workflowtype_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."workflow_workflowtype_enum" AS ENUM('Audit checklist', 'Planning request', 'Report finding', 'CAR/CAP', 'Internal audit report', 'Self assessment', 'Incidents')`,
    );
    await queryRunner.query(
      `ALTER TABLE "public"."workflow" ALTER COLUMN "workflowType" TYPE "public"."workflow_workflowtype_enum" USING "workflowType"::"text"::"public"."workflow_workflowtype_enum"`,
    );
    await queryRunner.query(`DROP TYPE "public"."workflow_workflowtype_enum_old"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
