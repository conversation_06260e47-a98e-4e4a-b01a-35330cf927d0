import { MigrationInterface, QueryRunner } from 'typeorm';

export class iamBase1652772831046 implements MigrationInterface {
  name = 'iamBase1652772831046';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Common status enum
    await queryRunner.query(`CREATE TYPE "common_status_enum" AS ENUM('active', 'inactive')`);

    await queryRunner.query(
      `CREATE TABLE "iam_meta_config" ("key" character varying NOT NULL, "lastTimeSync" character varying(30) NOT NULL, CONSTRAINT "PK_08a06b0c5069022f3d1fbd4cbdf" PRIMARY KEY ("key"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "feature" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "deleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "name" character varying NOT NULL, "description" character varying, "parentId" uuid, "order" integer NOT NULL DEFAULT '0', "isLeaf" boolean NOT NULL DEFAULT true, CONSTRAINT "PK_03930932f909ca4be8e33d16a2d" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "idx_feature_name" ON "feature" ("name") WHERE deleted = false`,
    );
    await queryRunner.query(
      `CREATE TYPE "workflow_workflowtype_enum" AS ENUM('Audit checklist', 'Planning request', 'Report finding', 'CAR/CAP', 'Internal audit report', 'Self assessment')`,
    );
    await queryRunner.query(
      `CREATE TYPE "workflow_approvertype_enum" AS ENUM('Without budget amount')`,
    );
    await queryRunner.query(`CREATE TYPE "workflow_status_enum" AS ENUM('Published', 'Inactive')`);
    await queryRunner.query(
      `CREATE TABLE "workflow" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "deleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "companyId" uuid NOT NULL, "workflowType" "workflow_workflowtype_enum" NOT NULL, "approverType" "workflow_approvertype_enum" NOT NULL DEFAULT 'Without budget amount', "status" "workflow_status_enum" NOT NULL, "createdUserId" uuid, "updatedUserId" uuid, "description" character varying, "version" character varying NOT NULL, CONSTRAINT "PK_eb5e4cc1a9ef2e94805b676751b" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "idx_workflow_version_companyId_workflowType" ON "workflow" ("version", "companyId", "workflowType") WHERE deleted = false`,
    );
    await queryRunner.query(
      `CREATE TYPE "workflow_role_permission_enum" AS ENUM('creator', 'approver', 'reviewer', 'auditor', 'owner/manager', 'close_out', 'reviewer1', 'reviewer2', 'reviewer3', 'reviewer4', 'reviewer5', 'verification', 'publisher')`,
    );
    await queryRunner.query(
      `CREATE TABLE "workflow_role" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "deleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "workflowId" uuid NOT NULL, "roleId" uuid NOT NULL, "permission" "workflow_role_permission_enum" NOT NULL, "createdUserId" uuid, "updatedUserId" uuid, CONSTRAINT "PK_29bd686bc4cf5eac541d526e3ef" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "user_role_permission" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "deleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "userId" character varying NOT NULL, "roleId" uuid NOT NULL, "permissionId" uuid NOT NULL, "featureName" character varying NOT NULL, "actionName" character varying NOT NULL, CONSTRAINT "PK_496b9eec633c366ce6a4a9636cf" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "user_role" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "deleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "userId" uuid NOT NULL, "roleId" uuid NOT NULL, CONSTRAINT "UQ_7b4e17a669299579dfa55a3fc35" UNIQUE ("userId", "roleId"), CONSTRAINT "PK_fb2e442d14add3cefbdf33c4561" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`CREATE TYPE "role_status_enum" AS ENUM('active', 'inactive')`);
    await queryRunner.query(
      `CREATE TABLE "role" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "deleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "name" citext NOT NULL, "description" character varying, "status" "role_status_enum" DEFAULT 'active', "companyId" uuid, CONSTRAINT "PK_b36bcfe02fc8de3c57a8b2391c2" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "idx_role_name_companyId_null" ON "role" ("name") WHERE "companyId" is NULL AND deleted = false`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "idx_role_name_companyId" ON "role" ("name", "companyId") WHERE deleted = false`,
    );
    await queryRunner.query(
      `CREATE TABLE "role_permission" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "deleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "roleId" uuid NOT NULL, "permissionId" uuid NOT NULL, CONSTRAINT "PK_96c8f1fd25538d3692024115b47" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "idx_role_permission_roleId_permissionId" ON "role_permission" ("roleId", "permissionId") WHERE deleted = false`,
    );
    await queryRunner.query(
      `CREATE TABLE "permission" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "deleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "featureId" uuid NOT NULL, "actionId" uuid NOT NULL, CONSTRAINT "PK_3b8b97af9d9d8807e41e6f48362" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "idx_permission_featureId_actionId" ON "permission" ("featureId", "actionId") WHERE deleted = false`,
    );
    await queryRunner.query(
      `CREATE TABLE "action" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "deleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "name" character varying NOT NULL, CONSTRAINT "PK_2d9db9cf5edfbbae74eb56e3a39" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "idx_action_name" ON "action" ("name") WHERE deleted = false`,
    );
    await queryRunner.query(
      `ALTER TABLE "feature" ADD CONSTRAINT "FK_d4a28a8e70d450a412bf0cfb52a" FOREIGN KEY ("parentId") REFERENCES "feature"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "workflow_role" ADD CONSTRAINT "FK_0ac61fda83a9f5222ba3995b29a" FOREIGN KEY ("roleId") REFERENCES "role"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "workflow_role" ADD CONSTRAINT "FK_cccf08f692e06813cf87b6e2d67" FOREIGN KEY ("workflowId") REFERENCES "workflow"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_role_permission" ADD CONSTRAINT "FK_243c52eb9d0be9779b200d996e8" FOREIGN KEY ("roleId") REFERENCES "role"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_role_permission" ADD CONSTRAINT "FK_4e6becc6a16e24aedb8c2f677a6" FOREIGN KEY ("permissionId") REFERENCES "permission"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_role" ADD CONSTRAINT "FK_dba55ed826ef26b5b22bd39409b" FOREIGN KEY ("roleId") REFERENCES "role"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "role_permission" ADD CONSTRAINT "FK_e3130a39c1e4a740d044e685730" FOREIGN KEY ("roleId") REFERENCES "role"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "role_permission" ADD CONSTRAINT "FK_72e80be86cab0e93e67ed1a7a9a" FOREIGN KEY ("permissionId") REFERENCES "permission"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "permission" ADD CONSTRAINT "FK_d5660ed7086e3991e4a292275e8" FOREIGN KEY ("featureId") REFERENCES "feature"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "permission" ADD CONSTRAINT "FK_54b459f3ffe8a2420c1bb0aea5a" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "permission" DROP CONSTRAINT "FK_54b459f3ffe8a2420c1bb0aea5a"`,
    );
    await queryRunner.query(
      `ALTER TABLE "permission" DROP CONSTRAINT "FK_d5660ed7086e3991e4a292275e8"`,
    );
    await queryRunner.query(
      `ALTER TABLE "role_permission" DROP CONSTRAINT "FK_72e80be86cab0e93e67ed1a7a9a"`,
    );
    await queryRunner.query(
      `ALTER TABLE "role_permission" DROP CONSTRAINT "FK_e3130a39c1e4a740d044e685730"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_role" DROP CONSTRAINT "FK_dba55ed826ef26b5b22bd39409b"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_role_permission" DROP CONSTRAINT "FK_4e6becc6a16e24aedb8c2f677a6"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_role_permission" DROP CONSTRAINT "FK_243c52eb9d0be9779b200d996e8"`,
    );
    await queryRunner.query(
      `ALTER TABLE "workflow_role" DROP CONSTRAINT "FK_cccf08f692e06813cf87b6e2d67"`,
    );
    await queryRunner.query(
      `ALTER TABLE "workflow_role" DROP CONSTRAINT "FK_0ac61fda83a9f5222ba3995b29a"`,
    );
    await queryRunner.query(
      `ALTER TABLE "feature" DROP CONSTRAINT "FK_d4a28a8e70d450a412bf0cfb52a"`,
    );
    await queryRunner.query(`DROP INDEX "idx_action_name"`);
    await queryRunner.query(`DROP TABLE "action"`);
    await queryRunner.query(`DROP INDEX "idx_permission_featureId_actionId"`);
    await queryRunner.query(`DROP TABLE "permission"`);
    await queryRunner.query(`DROP INDEX "idx_role_permission_roleId_permissionId"`);
    await queryRunner.query(`DROP TABLE "role_permission"`);
    await queryRunner.query(`DROP INDEX "idx_role_name_companyId"`);
    await queryRunner.query(`DROP INDEX "idx_role_name_companyId_null"`);
    await queryRunner.query(`DROP TABLE "role"`);
    await queryRunner.query(`DROP TYPE "role_status_enum"`);
    await queryRunner.query(`DROP TABLE "user_role"`);
    await queryRunner.query(`DROP TABLE "user_role_permission"`);
    await queryRunner.query(`DROP TABLE "workflow_role"`);
    await queryRunner.query(`DROP TYPE "workflow_role_permission_enum"`);
    await queryRunner.query(`DROP INDEX "idx_workflow_version_companyId_workflowType"`);
    await queryRunner.query(`DROP TABLE "workflow"`);
    await queryRunner.query(`DROP TYPE "workflow_status_enum"`);
    await queryRunner.query(`DROP TYPE "workflow_approvertype_enum"`);
    await queryRunner.query(`DROP TYPE "workflow_workflowtype_enum"`);
    await queryRunner.query(`DROP INDEX "idx_feature_name"`);
    await queryRunner.query(`DROP TABLE "feature"`);
    await queryRunner.query(`DROP TABLE "iam_meta_config"`);
  }
}
