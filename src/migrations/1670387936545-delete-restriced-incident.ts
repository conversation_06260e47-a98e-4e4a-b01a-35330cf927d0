import { MigrationInterface, QueryRunner } from 'typeorm';

export class deleteRestricedIncident1670387936545 implements MigrationInterface {
  name = 'deleteRestricedIncident1670387936545';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `delete from role_permission rp
where rp.id IN (select rp.id  from role_permission rp left join "role" r on r.id  = rp."roleId" 
left join "permission" p on rp."permissionId" = p.id
left join feature f on f.id = p."featureId"  
left join "action" a on a.id = p."actionId" 
where r."name" = 'Vetting Manager' and a."name" = 'Restricted' and f."name" = 'Quality Assurance::Incidents::Incidents');


delete  from user_role_permission urp 
where urp.id in (select urp.id from user_role_permission urp 
left join "role" r on r.id  = urp."roleId" 
left join "permission" p on urp."permissionId" = p.id
left join feature f on f.id = p."featureId"  
left join "action" a on a.id = p."actionId" 
where r."name" = 'Vetting Manager' and a."name" = 'Restricted' and f."name" = 'Quality Assurance::Incidents::Incidents');`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
