export class MySet {
  public static difference<T>(setA: Set<T>, setB: Set<T>) {
    // creating new set to store difference
    const differenceSet = new Set<T>();

    // iterate over the values
    for (const elem of setA) {
      // if the value[i] is not present
      // in setB add to the differenceSet
      if (!setB.has(elem)) differenceSet.add(elem);
    }

    // returns values of differenceSet
    return differenceSet;
  }

  public static intersect<T>(setA: Set<T>, setB: Set<T>) {
    // creating new set to store difference
    const intersectSet = new Set<T>();

    // iterate over the values
    for (const elem of setA) {
      // if the value[i] is present
      // in setB add to the intersectSet
      if (setB.has(elem)) intersectSet.add(elem);
    }

    // returns values of differenceSet
    return intersectSet;
  }
}

// MySet.difference(new Set([1, 2, 3, 4, 5]), new Set([2, 4]));
// MySet.intersect(new Set([1, 2, 3, 4, 5]), new Set([2, 4]));
