{"ts-node": {"files": true}, "files": ["./node_modules/svm-nest-lib/dist/types/global.d.ts"], "compilerOptions": {"module": "commonjs", "types": ["node"], "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "es2017", "sourceMap": false, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true}, "include": ["src/**/*"]}