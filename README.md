# Service Identity Access Management (IAM)

## Project Structure

...

## Description

[Nest](https://github.com/nestjs/nest) framework TypeScript starter repository.

## Installation

```bash
$ yarn install
```

## Running the app

```bash
# development
$ yarn start

# production mode
$ npm run start:prod
```

## Test

```bash
# unit tests
$ yarn test

# e2e tests
$ yarn test:e2e

# test coverage
$ yarn test:cov
```

## Migration (TypeORM - Postgres)

- [Refer link](https://orkhan.gitbook.io/typeorm/docs/migrations#how-migrations-work)
- Create file .env from file .env.example:

  - This file uses in generating migration script from change/new entity schema (compare with database schema)
  - Note:
    - File environment .yaml are used to provide environment variable into application
    - If application use Multi-tenant, DB config variables will be loaded dynamically

- While in developing (Local computer)

```bash
# Auto generate migration script from entity schema
yarn migration:generate <file_name>

# Create new empty migration file
yarn migration:create <file_name>

# Manually run migration scripts
yarn migration:run

# Revert latest changes
yarn migration:revert
```

## Support

Nest is an MIT-licensed open source project. It can grow thanks to the sponsors and support by the amazing backers. If you'd like to join them, please [read more here](https://docs.nestjs.com/support).

## Stay in touch

- Author - [Kamil Myśliwiec](https://kamilmysliwiec.com)
- Website - [https://nestjs.com](https://nestjs.com/)
- Twitter - [@nestframework](https://twitter.com/nestframework)

## License

Nest is [MIT licensed](LICENSE).

## Tag in codes

```
[TODO]: need to do
[DOING]: currently doing
[VERIFY]: Need to discuss more

```
